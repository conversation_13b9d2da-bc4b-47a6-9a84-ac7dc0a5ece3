# Liden SMS Dashboard

# PROMPT
# so use liden_sms_bo_ui to create all of it components to VUEjs app. basically convert this react to vue maintaining all other things

A modern SMS campaign management dashboard built with React, TypeScript, and Tailwind CSS.

## Features

- **SMS Campaign Management**: Create, send, and track SMS campaigns
- **Real-time Analytics**: Monitor delivery rates, open rates, and campaign performance
- **Contact Management**: Organize and manage your contact lists
- **Message Composer**: Intuitive interface for composing and scheduling messages
- **Delivery Tracking**: Track message delivery status and detailed analytics

## Technologies Used

- **React 18** - Modern React with hooks and functional components
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Modern UI component library
- **React Router** - Client-side routing
- **React Query** - Data fetching and state management

## Getting Started

### Prerequisites

- Node.js 18+ and npm (or yarn/pnpm)
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd liden-sms-dashboard
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
└── main.tsx           # Application entry point
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

