import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import Cookies from 'js-cookie'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// ============================================================================
// COOKIE MANAGEMENT UTILITIES
// ============================================================================

export interface CookieOptions {
  expires?: number | Date;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  httpOnly?: boolean;
}

export interface TokenData {
  token: string;
  clientData: string;
  expires: number;
  type: string;
  issuedAt: number;
}

/**
 * Secure cookie management utility for authentication tokens
 */
export class CookieManager {
  private static readonly TOKEN_COOKIE_NAME = 'liden_auth_token';
  private static readonly USER_COOKIE_NAME = 'liden_auth_user';
  private static readonly TOKEN_DATA_COOKIE_NAME = 'liden_token_data';

  /**
   * Default cookie options for secure token storage
   */
  private static getDefaultOptions(): CookieOptions {
    return {
      expires: 1, // 1 day default
      path: '/',
      secure: window.location.protocol === 'https:',
      sameSite: 'lax'
    };
  }

  /**
   * Store authentication token securely in cookies
   */
  static setAuthToken(tokenData: TokenData): void {
    try {
      const options = this.getDefaultOptions();

      // Calculate expiration based on API response
      const expirationHours = tokenData.expires;
      const expirationDate = new Date();

      if (tokenData.type === 'hour') {
        expirationDate.setHours(expirationDate.getHours() + expirationHours);
      } else if (tokenData.type === 'day') {
        expirationDate.setDate(expirationDate.getDate() + expirationHours);
      } else {
        // Default to hours
        expirationDate.setHours(expirationDate.getHours() + expirationHours);
      }

      options.expires = expirationDate;

      // Store the token
      Cookies.set(this.TOKEN_COOKIE_NAME, tokenData.token, options);

      // Store token metadata for expiration checking
      const tokenMetadata = {
        expires: tokenData.expires,
        type: tokenData.type,
        issuedAt: tokenData.issuedAt,
        expiresAt: expirationDate.getTime()
      };

      Cookies.set(this.TOKEN_DATA_COOKIE_NAME, JSON.stringify(tokenMetadata), options);

      console.log('Token stored in cookies with expiration:', expirationDate);
    } catch (error) {
      console.error('Failed to store auth token in cookies:', error);
      // Fallback to localStorage
      this.fallbackToLocalStorage(tokenData);
    }
  }

  /**
   * Get authentication token from cookies
   */
  static getAuthToken(): string | null {
    try {
      const token = Cookies.get(this.TOKEN_COOKIE_NAME);

      if (token && this.isTokenValid()) {
        return token;
      }

      if (token && !this.isTokenValid()) {
        console.warn('Token found but expired, clearing cookies');
        this.clearAuthData();
        return null;
      }

      // Fallback to localStorage for backward compatibility
      return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    } catch (error) {
      console.error('Failed to retrieve auth token from cookies:', error);
      return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }
  }

  /**
   * Check if the current token is valid (not expired)
   */
  static isTokenValid(): boolean {
    try {
      const tokenDataStr = Cookies.get(this.TOKEN_DATA_COOKIE_NAME);

      if (!tokenDataStr) {
        return false;
      }

      const tokenData = JSON.parse(tokenDataStr);
      const now = Date.now();

      return now < tokenData.expiresAt;
    } catch (error) {
      console.error('Failed to validate token:', error);
      return false;
    }
  }

  /**
   * Store user data in cookies
   */
  static setUserData(userData: any): void {
    try {
      const options = this.getDefaultOptions();
      Cookies.set(this.USER_COOKIE_NAME, JSON.stringify(userData), options);
    } catch (error) {
      console.error('Failed to store user data in cookies:', error);
      localStorage.setItem('auth_user', JSON.stringify(userData));
    }
  }

  /**
   * Get user data from cookies
   */
  static getUserData(): any | null {
    try {
      const userData = Cookies.get(this.USER_COOKIE_NAME);

      if (userData) {
        return JSON.parse(userData);
      }

      // Fallback to localStorage
      const localUserData = localStorage.getItem('auth_user') || sessionStorage.getItem('auth_user');
      return localUserData ? JSON.parse(localUserData) : null;
    } catch (error) {
      console.error('Failed to retrieve user data from cookies:', error);
      const localUserData = localStorage.getItem('auth_user') || sessionStorage.getItem('auth_user');
      return localUserData ? JSON.parse(localUserData) : null;
    }
  }

  /**
   * Clear all authentication data from cookies and localStorage
   */
  static clearAuthData(): void {
    try {
      // Clear cookies
      Cookies.remove(this.TOKEN_COOKIE_NAME, { path: '/' });
      Cookies.remove(this.USER_COOKIE_NAME, { path: '/' });
      Cookies.remove(this.TOKEN_DATA_COOKIE_NAME, { path: '/' });

      // Clear localStorage and sessionStorage for backward compatibility
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      sessionStorage.removeItem('auth_token');
      sessionStorage.removeItem('auth_user');

      console.log('All authentication data cleared');
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }

  /**
   * Get token expiration info
   */
  static getTokenExpirationInfo(): { expiresAt: Date | null; isExpired: boolean; timeRemaining: number } {
    try {
      const tokenDataStr = Cookies.get(this.TOKEN_DATA_COOKIE_NAME);

      if (!tokenDataStr) {
        return { expiresAt: null, isExpired: true, timeRemaining: 0 };
      }

      const tokenData = JSON.parse(tokenDataStr);
      const expiresAt = new Date(tokenData.expiresAt);
      const now = Date.now();
      const isExpired = now >= tokenData.expiresAt;
      const timeRemaining = Math.max(0, tokenData.expiresAt - now);

      return { expiresAt, isExpired, timeRemaining };
    } catch (error) {
      console.error('Failed to get token expiration info:', error);
      return { expiresAt: null, isExpired: true, timeRemaining: 0 };
    }
  }

  /**
   * Fallback to localStorage when cookies fail
   */
  private static fallbackToLocalStorage(tokenData: TokenData): void {
    try {
      localStorage.setItem('auth_token', tokenData.token);
      localStorage.setItem('token_data', JSON.stringify({
        expires: tokenData.expires,
        type: tokenData.type,
        issuedAt: tokenData.issuedAt
      }));
      console.warn('Fallback: Token stored in localStorage');
    } catch (error) {
      console.error('Failed to store token in localStorage fallback:', error);
    }
  }

  /**
   * Migrate existing localStorage tokens to cookies
   */
  static migrateFromLocalStorage(): void {
    try {
      const token = localStorage.getItem('auth_token');
      const userData = localStorage.getItem('auth_user');

      if (token) {
        // Create token data with default expiration
        const tokenData: TokenData = {
          token,
          clientData: '',
          expires: 1,
          type: 'hour',
          issuedAt: Date.now()
        };

        this.setAuthToken(tokenData);

        if (userData) {
          this.setUserData(JSON.parse(userData));
        }

        // Clear localStorage after successful migration
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');

        console.log('Successfully migrated tokens from localStorage to cookies');
      }
    } catch (error) {
      console.error('Failed to migrate tokens from localStorage:', error);
    }
  }
}
