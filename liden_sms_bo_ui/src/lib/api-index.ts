/**
 * Comprehensive API Index for Liden SMS Platform
 * 
 * This file provides a centralized access point for all API methods
 * organized by service categories. All methods are properly typed
 * and documented for easy integration.
 * 
 * Usage:
 * import { LidenAPI } from '@/lib/api-index';
 * 
 * // Authentication
 * const loginResult = await LidenAPI.auth.login(loginData);
 * 
 * // SMS Services
 * const smsResult = await LidenAPI.sms.sendBlast(smsData);
 * 
 * // Survey Services
 * const surveyResult = await LidenAPI.survey.create(surveyData);
 */

// Import all API classes
import { AuthAPI, SMSAPI, SurveyAPI, UtilityAPI } from './api';
import {
  USSDAPI,
  ContactAPI,
  ClientAPI,
  VoiceAPI,
  SchedulerAPI,
  WebhookAPI,
  PremiumContentAPI
} from './api-extended';

// Export the main API service and types for direct access
export { apiService, endpoints } from './api';
export type { ApiResponse } from './api-types';

/**
 * Centralized API access point for all Liden services
 */
export const LidenAPI = {
  /**
   * Authentication Services
   * - User login/logout
   * - Password management
   * - Profile management
   */
  auth: AuthAPI,

  /**
   * SMS Services
   * - Bulk SMS sending
   * - SMS analytics and reporting
   * - Premium, shortcode, and alphanumeric SMS
   * - Message management and blacklisting
   */
  sms: SMSAPI,

  /**
   * Survey Services
   * - Survey creation and management
   * - Response collection and analysis
   * - Incentive management
   * - Question type configuration
   */
  survey: SurveyAPI,

  /**
   * Utility/Airtime Services
   * - Single and bulk airtime sending
   * - M-pesa payouts and B2B transactions
   * - Service activation and approvals
   * - Transaction reporting
   */
  utility: UtilityAPI,

  /**
   * USSD Services
   * - USSD gateway integration
   * - App and access point management
   * - Service configuration
   */
  ussd: USSDAPI,

  /**
   * Contact Management
   * - Contact viewing and editing
   * - Contact list management
   */
  contact: ContactAPI,

  /**
   * Client Management
   * - User management and permissions
   * - M-pesa configuration
   * - Account activation/deactivation
   * - Wallet and billing management
   * - Audit logs and invoicing
   * - Dashboard statistics
   */
  client: ClientAPI,

  /**
   * Dashboard Services (now part of client API)
   * - Comprehensive wallet information
   * - Bulk SMS usage analytics
   * - Enhanced dashboard statistics
   * - Real-time metrics and reporting
   */
  dashboard: ClientAPI,

  /**
   * Voice Services
   * - VOIP type management
   */
  voice: VoiceAPI,

  /**
   * Scheduler Services
   * - Service execution scheduling
   * - Automated task management
   */
  scheduler: SchedulerAPI,

  /**
   * Webhook Services
   * - AT callback validation
   * - C2B confirmation handling
   */
  webhook: WebhookAPI,

  /**
   * Premium Content Services
   * - Subscription management
   * - Message reporting
   */
  premium: PremiumContentAPI,
};

/**
 * Service-specific API exports for modular usage
 */
export {
  // Core APIs
  AuthAPI,
  SMSAPI,
  SurveyAPI,
  UtilityAPI,
  
  // Extended APIs
  USSDAPI,
  ContactAPI,
  ClientAPI,
  VoiceAPI,
  SchedulerAPI,
  WebhookAPI,
  PremiumContentAPI,
};

/**
 * Common API patterns and utilities
 */
export const APIUtils = {
  /**
   * Standard pagination parameters
   */
  createPaginationParams: (page: number = 1, limit: number = 10, sort?: string) => ({
    offset: (page - 1) * limit,
    limit,
    sort,
  }),

  /**
   * Standard date range parameters
   */
  createDateRangeParams: (startDate: Date, endDate: Date) => ({
    start: startDate.toISOString().split('T')[0],
    end: endDate.toISOString().split('T')[0],
  }),

  /**
   * Export parameters
   */
  createExportParams: (format: 'csv' | 'excel' = 'csv') => ({
    export: format === 'csv' ? 1 : 2,
  }),

  /**
   * Handle API response errors consistently
   */
  handleApiError: (error: any, defaultMessage: string = 'An error occurred') => {
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    if (error?.message) {
      return error.message;
    }
    return defaultMessage;
  },

  /**
   * Check if API response is successful
   */
  isSuccessResponse: (response: any): boolean => {
    return response?.success === true || response?.data !== null;
  },
};

/**
 * API Configuration and Constants
 */
export const APIConfig = {
  /**
   * Base URL for all API requests
   */
  BASE_URL: 'https://app.apiproxy.co',

  /**
   * Common headers for authenticated requests
   */
  AUTHENTICATED_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },

  /**
   * Request timeout in milliseconds
   */
  REQUEST_TIMEOUT: 30000,

  /**
   * Retry configuration
   */
  RETRY_CONFIG: {
    maxRetries: 3,
    retryDelay: 1000,
  },
};

/**
 * Default export for convenient access
 */
export default LidenAPI;
