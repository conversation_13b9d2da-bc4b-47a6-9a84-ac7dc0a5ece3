// Comprehensive TypeScript interfaces for Liden API

// ============================================================================
// COMMON TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginationParams {
  sort?: string;
  offset?: number;
  limit?: number;
  [key: string]: string | number | undefined;
}

export interface DateRangeParams {
  start?: string;
  end?: string;
  [key: string]: string | number | undefined;
}

export interface ExportParams {
  export?: boolean | number;
  [key: string]: string | number | boolean | undefined;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface LoginRequest {
  userName: string;
  countryCode: string;
  password: string;
  apigw: string;
}

export interface LoginResponse {
  code: number;
  message: string;
  data: {
    token: string;
    client_data: string;
    expires: number;
    type: string;
  };
}

export interface ForgotPasswordRequest {
  phoneNumber: string;
  countryCode: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface UserProfile {
  id: string;
  phoneNumber: string;
  countryCode: string;
  name: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}

// ============================================================================
// SMS TYPES
// ============================================================================

export interface SMSBlastRequest {
  shortCode: string;
  message: string;
  approval?: string;
  queryRecipient?: string;
  blast_name: string;
  uniqueId: string;
  isScheduled?: string;
  scheduleDate?: string;
  scheduleTime?: string;
  callbackURL?: string;
}

export interface SMSMessage {
  id: string;
  message: string;
  recipient: string;
  status: string;
  timestamp: string;
  shortCode?: string;
}

export interface SMSAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
}

// ============================================================================
// SURVEY TYPES
// ============================================================================

export interface SurveyCreateRequest {
  survey: {
    surveyName: string;
    surveyDesc: string;
    contactLists: string;
    surveyMode: {
      channelMode: string;
      channelSource: string;
    };
    autoReminder?: string;
    enableSavePoint?: string;
    enableWhitelist?: string;
    reminderPeriod?: string;
    optinMessage?: string;
    optoutMessage?: string;
    reminderMessage?: string;
    countryCode: string;
    startDate: string;
    stopDate: string;
  };
  questionnaire: {
    questionnareName: string;
    questionnareDesc: string;
    questionnareQuestions: QuestionnaireQuestion[];
  };
  incentive: {
    incentiveType: string;
    utilityId: string;
    rewardAmount: string;
    rewardName: string;
    mechanicLimit: string;
  };
}

export interface QuestionnaireQuestion {
  question: string;
  questionTypeId: string;
  questionChoices: QuestionChoice[];
}

export interface QuestionChoice {
  value: string;
  numbering: string;
  linkQuestionId?: string;
}

export interface SurveyResponse {
  id: string;
  surveyId: string;
  msisdn: string;
  responses: any[];
  timestamp: string;
}

export interface SurveyApplication {
  id: string;
  surveyName: string;
  status: string;
  clientId: string;
  createdDate: string;
}

// ============================================================================
// UTILITY/AIRTIME TYPES
// ============================================================================

export interface AirtimeSingleRequest {
  recipient: string;
  amount: string;
  currency: string;
  uniqueId: string;
  retry?: string;
  dialCode: string;
  callbackURL?: string;
}

export interface AirtimeBulkRequest {
  clientId?: string;
  countryCode: string;
  callbackUrl?: string;
  uniqueId: string;
  uploadedFile: File;
}

export interface MpesaPayoutRequest {
  amount: string;
  callback: string;
  merchantId: string;
  msisdn: string;
  narration: string;
  uniqueId: string | number;
}

export interface MpesaB2BPayoutRequest extends MpesaPayoutRequest {
  ReceivingPaybillNumber: string;
  accountNumber: string;
}

export interface UtilityTransaction {
  id: string;
  amount: string;
  recipient: string;
  status: string;
  statusDesc: string;
  timestamp: string;
  uniqueId: string;
}

// ============================================================================
// USSD TYPES
// ============================================================================

export interface USSDGatewayParams {
  msisdn: string;
  ussd_string: string;
  service_code: string;
  session_id: string;
  [key: string]: string | number;
}

export interface USSDApp {
  id: string;
  clientId: string;
  systemName: string;
  status: string;
}

export interface USSDAccessPoint {
  id: string;
  appId: string;
  serviceCode: string;
  systemName: string;
  status: string;
}

export interface USSDCreateAppRequest {
  clientId: string;
  systemName: string;
}

export interface USSDConfigureAccessPointRequest {
  appId: string;
  typeId: string;
  accessPoint: string;
  networkId: string;
  defaultMessage: string;
  callbackUrl: string;
  callbackPortNumber: string;
}

// ============================================================================
// CONTACT TYPES
// ============================================================================

export interface Contact {
  id: string;
  msisdn: string;
  listId: string;
  custom1?: string;
  custom2?: string;
  custom3?: string;
  custom4?: string;
  custom5?: string;
}

export interface ContactEditRequest {
  status?: string;
  custom1?: string;
  custom2?: string;
  custom3?: string;
  custom4?: string;
  custom5?: string;
}

export interface ContactListParams extends PaginationParams, DateRangeParams {
  msisdn?: string;
  listId?: string;
}

// ============================================================================
// CLIENT MANAGEMENT TYPES
// ============================================================================

export interface AddUserRequest {
  mobile: string;
  countryCode: string;
  emailAddress: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  roleId: string;
  permissionAcl?: string;
  clientId?: string;
}

export interface EditUserRequest {
  userMapId: string;
  roleId?: string;
  emailAddress?: string;
  callbackUrl?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  surName?: string;
  status?: string;
}

export interface UserPermissionsRequest {
  permissionAcl: string;
  user_mapId: string;
}

export interface MpesaConfigRequest {
  paybillNumber: string;
  orgName: string;
  maxPayouts: string;
  callbackUrl: string;
  mpesaUsername: string;
  mpesaPass: string;
  consumerKey: string;
  secretKey: string;
  clientId: string;
  ipWhitelist: string;
}

export interface ClientWallet {
  balance: number;
  currency: string;
  clientId: string;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  timestamp: string;
  details: string;
}

export interface Invoice {
  id: string;
  invoiceId: string;
  amount: number;
  clientId: string;
  serviceId: string;
  status: string;
  createdDate: string;
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardStats {
  totalSMS: number;
  totalSurveys: number;
  totalAirtime: number;
  totalRevenue: number;
  period: {
    start: string;
    end: string;
  };
}

// ============================================================================
// WALLET TYPES
// ============================================================================

export interface WalletResponse {
  clientId: string;
  clientName: string;
  balance: number;
  currency: string;
  threshold: number;
  status: string;
  lastUpdated: string;
  accountType: string;
  creditLimit?: number;
  alertThreshold: number;
  isActive: boolean;
}

// ============================================================================
// BULK SMS USAGE TYPES
// ============================================================================

export interface BulkUsageResponse {
  campaignId: string;
  campaignName: string;
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  totalPending: number;
  deliveryRate: number;
  failureRate: number;
  pendingRate: number;
  totalCost: number;
  costPerSMS: number;
  currency: string;
  status: number;
  createdDate: string;
  completedDate?: string;
  clientId: string;
  messageContent: string;
  recipientCount: number;
  deliveryStats: {
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
  };
  costBreakdown: {
    totalCost: number;
    currency: string;
    costPerMessage: number;
  };
}

export interface BulkUsageParams extends PaginationParams {
  limit?: number;
  status?: number;
  clientId?: string;
  startDate?: string;
  endDate?: string;
}

// ============================================================================
// DASHBOARD STATISTICS TYPES
// ============================================================================

export interface DashboardStatsResponse {
  totalMessagesSent: number;
  totalMessagesDelivered: number;
  totalMessagesFailed: number;
  totalMessagesPending: number;
  deliverySuccessRate: number;
  failureRate: number;
  pendingRate: number;
  totalCost: number;
  averageCostPerMessage: number;
  currency: string;
  period: {
    start: string;
    end: string;
  };
  dailyStats: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
    cost: number;
  }>;
  campaignSummary: {
    totalCampaigns: number;
    activeCampaigns: number;
    completedCampaigns: number;
    failedCampaigns: number;
  };
  topPerformingCampaigns: Array<{
    campaignId: string;
    campaignName: string;
    deliveryRate: number;
    totalSent: number;
    totalCost: number;
  }>;
}

// ============================================================================
// BULK MESSAGES TYPES
// ============================================================================

export interface BulkMessage {
  count: string;
  outbox_id: string;
  msisdn: string;
  short_code: string;
  message: string;
  message_length: string;
  sms_cost: string;
  alert_type: string;
  network: string;
  description: string;
  created_at: string;
}

export interface BulkMessagesResponse {
  total_count: string;
  data: BulkMessage[];
}

export interface BulkMessagesParams extends PaginationParams, DateRangeParams {
  search?: string;
  network?: string;
  type?: string;
  status?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

// ============================================================================
// CONTACT GROUPS TYPES
// ============================================================================

export interface ContactGroup {
  total_count: string;
  list_id: string;
  group_name: string;
  description: string;
  status: string;
  can_upload: string;
  custom1: string;
  custom2: string;
  custom3: string;
  custom4: string;
  custom5: string;
  deleted_on: string | null;
  created_at: string;
  created_by: string;
}

export interface ContactGroupsResponse {
  total_count: string;
  data: ContactGroup[];
}

export interface ContactGroupsParams extends PaginationParams {
  status?: string;
  search?: string;
}

// ============================================================================
// SENDER IDS TYPES
// ============================================================================

export interface SenderId {
  total_count: string;
  id: string;
  sender_id: string;
  sender_type: string;
  sStatus: string;
  short_code: string;
  status: string;
  client_id: string;
  client_name: string;
  client_email: string;
}

export interface SenderIdsResponse {
  total_count: string;
  data: SenderId[];
}

export interface SenderIdsParams extends PaginationParams {
  typeId?: string;
  status?: boolean;
}

export interface DashboardStatsParams extends DateRangeParams {
  clientId?: string;
  campaignId?: string;
  groupBy?: 'day' | 'week' | 'month';
}

// ============================================================================
// SCHEDULER TYPES
// ============================================================================

export interface SchedulerExecuteRequest {
  serviceId: string;
  api_token: string;
  typeId?: string;
  productId?: string;
  start?: string;
  end?: string;
}

// ============================================================================
// WEBHOOK TYPES
// ============================================================================

export interface ATCallbackData {
  // AT callback validation data structure
  [key: string]: any;
}

export interface C2BConfirmationData {
  // C2B confirmation data structure
  [key: string]: any;
}

// ============================================================================
// PREMIUM CONTENT TYPES
// ============================================================================

export interface MessageReport {
  id: string;
  productId: string;
  shortCode: string;
  campaignId: string;
  reportId: string;
  timestamp: string;
}

export interface MessageReportParams extends PaginationParams, DateRangeParams {
  productId?: string;
  shortCode?: string;
  clientId?: string;
  campaignId?: string;
  reportId?: string;
}

// ============================================================================
// VOICE TYPES
// ============================================================================

export interface VoiceType {
  id: string;
  name: string;
  description: string;
}
