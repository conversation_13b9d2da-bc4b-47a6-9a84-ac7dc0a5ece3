// Extended API methods for Liden services
import { apiService, endpoints } from './api';
import {
  ApiResponse,
  PaginationParams,
  DateRangeParams,
  ExportParams,
  USSDGatewayParams,
  USSDApp,
  USSDAccessPoint,
  USSDCreateAppRequest,
  USSDConfigureAccessPointRequest,
  Contact,
  ContactEditRequest,
  ContactListParams,
  AddUserRequest,
  EditUserRequest,
  UserPermissionsRequest,
  MpesaConfigRequest,
  ClientWallet,
  AuditLog,
  Invoice,
  DashboardStats,
  WalletResponse,
  BulkUsageResponse,
  BulkUsageParams,
  DashboardStatsResponse,
  DashboardStatsParams,
  BulkMessagesResponse,
  BulkMessagesParams,
  ContactGroupsResponse,
  ContactGroupsParams,
  SenderIdsResponse,
  SenderIdsParams,
  SchedulerExecuteRequest,
  ATCallbackData,
  C2BConfirmationData,
  MessageReport,
  MessageReportParams,
  VoiceType
} from './api-types';

// ============================================================================
// USSD API METHODS
// ============================================================================

export class USSDAPI {
  /**
   * Safaricom USSD gateway
   */
  static async safaricomGateway(params: USSDGatewayParams): Promise<ApiResponse<any>> {
    return apiService.get(endpoints.ussd.safaricomGateway, params);
  }

  /**
   * View USSD types
   */
  static async getTypes(): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.ussd.types);
  }

  /**
   * View access points
   */
  static async getAccessPoints(params?: PaginationParams & ExportParams & {
    clientId?: string;
    serviceCode?: string;
    systemName?: string;
    status?: string;
    start?: string;
    end?: string;
  }): Promise<ApiResponse<USSDAccessPoint[]>> {
    return apiService.get<USSDAccessPoint[]>(endpoints.ussd.accessPoints, params);
  }

  /**
   * View USSD apps
   */
  static async getApps(params?: PaginationParams & ExportParams & {
    clientId?: string;
    serviceCode?: string;
    systemName?: string;
    status?: string;
  }): Promise<ApiResponse<USSDApp[]>> {
    return apiService.get<USSDApp[]>(endpoints.ussd.apps, params);
  }

  /**
   * Create new USSD app
   */
  static async createApp(request: USSDCreateAppRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.ussd.createApp, request);
  }

  /**
   * Configure access point
   */
  static async configureAccessPoint(request: USSDConfigureAccessPointRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.ussd.configureAccessPoint, request);
  }
}

// ============================================================================
// CONTACT MANAGEMENT API METHODS
// ============================================================================

export class ContactAPI {
  /**
   * View contacts
   */
  static async getContacts(params?: ContactListParams): Promise<ApiResponse<Contact[]>> {
    return apiService.get<Contact[]>(endpoints.contact.view, params);
  }

  /**
   * Edit mobile entry
   */
  static async editEntry(contactId: string, request: ContactEditRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.contact.edit.replace('{contactId}', contactId), request);
  }

  /**
   * Get contact groups
   */
  static async getGroups(params?: ContactGroupsParams): Promise<ApiResponse<ContactGroupsResponse>> {
    return apiService.get<ContactGroupsResponse>(endpoints.contact.groups, params);
  }
}

// ============================================================================
// CLIENT MANAGEMENT API METHODS
// ============================================================================

export class ClientAPI {
  /**
   * Add system users
   */
  static async addUser(request: AddUserRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.addUser, request);
  }

  /**
   * Edit system user
   */
  static async editUser(request: EditUserRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.editUser, request);
  }

  /**
   * Add client permissions
   */
  static async addPermissions(request: UserPermissionsRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.addPermissions, request);
  }

  /**
   * Configure M-pesa payouts
   */
  static async configureMpesa(request: MpesaConfigRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.configureMpesa, request);
  }

  /**
   * Edit M-pesa paybill
   */
  static async editMpesa(request: any): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.editMpesa, request);
  }

  /**
   * Activate/deactivate client account
   */
  static async activateAccount(request: { status: string; clientId: string; reason: string }): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.client.activateAccount, request);
  }

  /**
   * View configured countries
   */
  static async getCountries(): Promise<ApiResponse<any[]>> {
    return apiService.get(endpoints.client.countries);
  }

  /**
   * View client wallet
   */
  static async getWallet(clientId?: string): Promise<ApiResponse<ClientWallet>> {
    return apiService.get<ClientWallet>(endpoints.client.wallet, { clientId });
  }

  /**
   * View user audit logs
   */
  static async getAuditLogs(params?: PaginationParams & DateRangeParams & ExportParams & {
    filter?: string;
    userId?: string;
  }): Promise<ApiResponse<AuditLog[]>> {
    return apiService.get<AuditLog[]>(endpoints.client.auditLogs, params);
  }

  /**
   * View user invoices
   */
  static async getInvoices(params?: PaginationParams & DateRangeParams & ExportParams & {
    userMapId?: string;
    invoiceId?: string;
    invoiceAmount?: string;
    clientId?: string;
    serviceId?: string;
    invoiceReference?: string;
  }): Promise<ApiResponse<Invoice[]>> {
    return apiService.get<Invoice[]>(endpoints.client.invoices, params);
  }

  /**
   * View dashboard stats
   */
  static async getDashboardStats(params: DateRangeParams & { userMapId?: string }): Promise<ApiResponse<DashboardStats>> {
    return apiService.get<DashboardStats>(endpoints.client.dashboardStats, params);
  }

  /**
   * Get enhanced wallet information
   */
  static async getWalletInfo(clientId?: string): Promise<ApiResponse<WalletResponse>> {
    return apiService.get<WalletResponse>(endpoints.client.wallet, { clientId });
  }

  /**
   * Get bulk SMS usage statistics
   */
  static async getBulkUsage(params?: BulkUsageParams): Promise<ApiResponse<BulkUsageResponse[]>> {
    return apiService.get<BulkUsageResponse[]>(endpoints.client.bulkUsage, params);
  }

  /**
   * Get enhanced dashboard statistics
   */
  static async getDashboardStatistics(params: DashboardStatsParams): Promise<ApiResponse<DashboardStatsResponse>> {
    return apiService.get<DashboardStatsResponse>(endpoints.client.dashboardStats, params);
  }

  /**
   * Get bulk messages with filtering and pagination
   */
  static async getBulkMessages(params?: BulkMessagesParams): Promise<ApiResponse<BulkMessagesResponse>> {
    return apiService.get<BulkMessagesResponse>(endpoints.client.bulkMessages, params);
  }

  /**
   * Get sender IDs
   */
  static async getSenderIds(params?: SenderIdsParams): Promise<ApiResponse<SenderIdsResponse>> {
    return apiService.get<SenderIdsResponse>(endpoints.client.senderIds, params);
  }
}



// ============================================================================
// VOICE API METHODS
// ============================================================================

export class VoiceAPI {
  /**
   * View VOIP types
   */
  static async getTypes(): Promise<ApiResponse<VoiceType[]>> {
    return apiService.get<VoiceType[]>(endpoints.voice.types);
  }
}

// ============================================================================
// SCHEDULER API METHODS
// ============================================================================

export class SchedulerAPI {
  /**
   * Execute services
   */
  static async execute(request: SchedulerExecuteRequest): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.scheduler.execute, request);
  }
}

// ============================================================================
// WEBHOOK API METHODS
// ============================================================================

export class WebhookAPI {
  /**
   * AT callback validation
   */
  static async atCallback(data: ATCallbackData): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.webhooks.atCallback, data);
  }

  /**
   * C2B confirmation
   */
  static async c2bConfirmation(data: C2BConfirmationData): Promise<ApiResponse<any>> {
    return apiService.post(endpoints.webhooks.c2bConfirmation, data);
  }
}

// ============================================================================
// PREMIUM CONTENT API METHODS
// ============================================================================

export class PremiumContentAPI {
  /**
   * View subscription outbox
   */
  static async getMessageReports(params?: MessageReportParams): Promise<ApiResponse<MessageReport[]>> {
    return apiService.get<MessageReport[]>(endpoints.subscription.messageReports, params);
  }
}

// Export all API classes for easy access
export {
  // From main api.ts file
  AuthAPI,
  SMSAPI,
  SurveyAPI,
  UtilityAPI
} from './api';

// Export extended API classes - DashboardAPI methods are now part of ClientAPI
