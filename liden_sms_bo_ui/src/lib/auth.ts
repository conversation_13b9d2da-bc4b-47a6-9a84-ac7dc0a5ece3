import { createContext, useContext } from 'react';
import { LidenAPI } from './api-index';
import type { LoginRequest } from './api-types';
import { CookieManager, type TokenData } from './utils';

export interface User {
  id: string;
  phoneNumber: string;
  countryCode: string;
  name: string;
  email?: string;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (phoneNumber: string, password: string, countryCode: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => void;
  forgotPassword: (phoneNumber: string, countryCode: string) => Promise<boolean>;
  resetPassword: (token: string, newPassword: string) => Promise<boolean>;
  isLoading: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Real API authentication functions
export const realLogin = async (phoneNumber: string, password: string, countryCode: string): Promise<User | null> => {
  try {
    // Extract numeric country code from the format "KE (+254)"
    const numericCountryCode = countryCode.match(/\+(\d+)/)?.[1] || countryCode;

    // Prepare login request body according to API specification
    const loginData: LoginRequest = {
      userName: phoneNumber,
      countryCode: numericCountryCode,
      password: password,
      apigw: "WEB_GW"
    };

    console.log('Attempting login with:', { ...loginData, password: '[REDACTED]' });

    // Make API call to login endpoint using the new API structure
    const response = await LidenAPI.auth.login(loginData);

    if (response.success && response.data && response.data.data) {
      // Decode client_data JWT to get user information
      let userInfo = null;
      try {
        const clientData = response.data.data.client_data;
        const payload = JSON.parse(atob(clientData.split('.')[1]));
        userInfo = payload;
      } catch (error) {
        console.warn('Could not decode client_data JWT:', error);
      }

      // Extract user information from API response
      const user: User = {
        id: userInfo?.userId || userInfo?.clientId || '1',
        phoneNumber,
        countryCode: numericCountryCode,
        name: userInfo?.clientName || 'Liden User',
        email: userInfo?.emailAddress || `${phoneNumber}@liden.co.ke`
      };

      // Store the authentication token from response using secure cookies
      const token = response.data.data.token;
      if (token) {
        console.log('Raw token from API:', token);
        console.log('Token length:', token.length);

        // Create token data object with API response information
        const tokenData: TokenData = {
          token,
          clientData: response.data.data.client_data,
          expires: response.data.data.expires || 1,
          type: response.data.data.type || 'hour',
          issuedAt: Date.now()
        };

        // Store token securely in cookies
        CookieManager.setAuthToken(tokenData);

        // Store user data in cookies
        CookieManager.setUserData(user);

        // Set token in API service for future requests
        const { apiService } = await import('./api-index');
        apiService.setAuthToken(token);
        console.log('Token set in API service and stored in secure cookies');
      }

      console.log('Login successful:', user);
      console.log('Token stored securely with expiration:', response.data.data.expires, response.data.data.type);
      return user;
    } else {
      console.error('Login failed:', response.message || 'Unknown error');
      return null;
    }
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};

// Mock authentication functions (fallback for development)
export const mockLogin = async (phoneNumber: string, password: string, countryCode: string): Promise<User | null> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock validation - accept specific credentials for demo
  // Real API endpoint: POST /account/v1/grant_access
  // Real body: { "userName": "**********", "countryCode": "254", "password": "Keny@1-Nb1", "apigw": "WEB_GW" }
  if (phoneNumber === '**********' && password === 'Keny@1-Nb1' && countryCode === '254') {
    const user: User = {
      id: '1',
      phoneNumber,
      countryCode,
      name: 'Liden User',
      email: `${phoneNumber}@liden.co.ke`
    };

    // Store in cookies for persistence
    const mockToken = 'UGtJNForT1dzTXZPNmREN2hjSmMzbU40VzlEdWpFWDhvRzhQRWZSMWNhK29xZEVvRzVPVlVuR0hiVTdoVWRjdlBNRlJmeE1kcnlZVXBubTAzL1R6OGFTakl5cE1VTytnY1NCUmlKVzB5OVM4N0U1RTVKMzE2T2JZUFJINFhnakQvNFhXMVZtR0ZVdjVpZW9mVmpSckNYTGQyYmhyblgzNG9HWEtJMGxjTDBCS1Nsc3JkSU0xT1JPMW5CZnIwS0l4blB6VUJqbEtoV216RHhVUXJ3a24vUTYzTmVVZHlEcXRpRFZVZDRPazJJbTN1WEJrVG1xanRGNXZ5ajIySHpiS1ZxSjZKWGhLcjVpSk85ZVR3QjNWYlFzMFFkQkxtOWErUU9ydDR1eCtHMUs4emhOcmdqQlQvbitxSHU0dm5PQlRuTzNubjhwckZpSHF5ZUdySHlOTTdBPT06Ojb8b1/NRpXBtQKOZYpJtQk=';

    const tokenData: TokenData = {
      token: mockToken,
      clientData: '',
      expires: 1,
      type: 'hour',
      issuedAt: Date.now()
    };

    CookieManager.setAuthToken(tokenData);
    CookieManager.setUserData(user);

    return user;
  }

  return null;
};

export const mockLogout = () => {
  CookieManager.clearAuthData();
};

export const mockForgotPassword = async (phoneNumber: string, countryCode: string): Promise<boolean> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock success for any valid phone number
  if (phoneNumber && phoneNumber.length >= 9) {
    // In real app, this would send SMS/email with reset token
    console.log(`Password reset token sent to ${countryCode} ${phoneNumber}`);
    return true;
  }
  
  return false;
};

export const mockResetPassword = async (token: string, newPassword: string): Promise<boolean> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock success for any token and password
  if (token && newPassword && newPassword.length >= 6) {
    console.log('Password reset successful');
    return true;
  }
  
  return false;
};

export const getStoredUser = (): User | null => {
  try {
    // Try to get user from cookies first
    const userData = CookieManager.getUserData();
    const token = CookieManager.getAuthToken();

    if (userData && token && CookieManager.isTokenValid()) {
      return userData;
    }

    // If token is expired, clear all auth data
    if (userData && token && !CookieManager.isTokenValid()) {
      console.warn('Token expired, clearing authentication data');
      CookieManager.clearAuthData();
      return null;
    }

    return null;
  } catch (error) {
    console.error('Error parsing stored user:', error);
    return null;
  }
};

export const isTokenValid = (): boolean => {
  return CookieManager.isTokenValid();
};
