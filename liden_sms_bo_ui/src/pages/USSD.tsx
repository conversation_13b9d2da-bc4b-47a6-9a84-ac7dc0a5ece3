import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Smartphone, 
  Play, 
  Pause,
  Settings,
  Users,
  Activity,
  TrendingUp,
  Filter,
  Plus,
  Code,
  MessageSquare
} from "lucide-react";

const USSD = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [ussdCode, setUssdCode] = useState("");
  const [menuText, setMenuText] = useState("");
  const [responseAction, setResponseAction] = useState("");

  // Mock data for USSD sessions
  const ussdSessions = [
    {
      id: "1",
      code: "*123#",
      phoneNumber: "+254704050143",
      session: "Balance Inquiry",
      status: "Completed",
      duration: "45s",
      steps: 3,
      timestamp: "2024-01-16 14:30",
    },
    {
      id: "2",
      code: "*456#",
      phoneNumber: "+254712345678",
      session: "Airtime Purchase",
      status: "Completed",
      duration: "2m 15s",
      steps: 5,
      timestamp: "2024-01-16 13:45",
    },
    {
      id: "3",
      code: "*789#",
      phoneNumber: "+254798765432",
      session: "Service Menu",
      status: "Abandoned",
      duration: "12s",
      steps: 1,
      timestamp: "2024-01-16 12:20",
    },
    {
      id: "4",
      code: "*123#",
      phoneNumber: "+254723456789",
      session: "Balance Inquiry",
      status: "Active",
      duration: "ongoing",
      steps: 2,
      timestamp: "2024-01-16 14:35",
    },
  ];

  const columns = [
    {
      accessorKey: "code",
      header: "USSD Code",
      cell: ({ row }: any) => (
        <Badge variant="outline" className="font-mono text-lg">
          {row.getValue("code")}
        </Badge>
      ),
    },
    {
      accessorKey: "phoneNumber",
      header: "Phone Number",
      cell: ({ row }: any) => (
        <span className="font-mono">{row.getValue("phoneNumber")}</span>
      ),
    },
    {
      accessorKey: "session",
      header: "Session Type",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        const colors = {
          Completed: "default",
          Active: "secondary",
          Abandoned: "destructive",
        };
        return (
          <Badge variant={colors[status as keyof typeof colors] as any}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell: ({ row }: any) => (
        <span className="text-sm">{row.getValue("duration")}</span>
      ),
    },
    {
      accessorKey: "steps",
      header: "Steps",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("steps")}</span>
      ),
    },
    {
      accessorKey: "timestamp",
      header: "Started",
      cell: ({ row }: any) => (
        <span className="text-sm text-muted-foreground">{row.getValue("timestamp")}</span>
      ),
    },
  ];

  // Mock USSD menu structure
  const ussdMenus = [
    {
      id: "1",
      code: "*123#",
      name: "Main Menu",
      status: "Active",
      sessions: 1250,
      completion: 87,
    },
    {
      id: "2",
      code: "*456#",
      name: "Airtime Services",
      status: "Active",
      sessions: 890,
      completion: 92,
    },
    {
      id: "3",
      code: "*789#",
      name: "Customer Support",
      status: "Paused",
      sessions: 450,
      completion: 65,
    },
  ];

  const menuColumns = [
    {
      accessorKey: "code",
      header: "USSD Code",
      cell: ({ row }: any) => (
        <Badge variant="outline" className="font-mono text-lg">
          {row.getValue("code")}
        </Badge>
      ),
    },
    {
      accessorKey: "name",
      header: "Menu Name",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("name")}</span>
      ),
    },
    {
      accessorKey: "sessions",
      header: "Sessions",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("sessions").toLocaleString()}</span>
      ),
    },
    {
      accessorKey: "completion",
      header: "Completion Rate",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("completion")}%</span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "Active" ? "default" : "secondary"}>
            {status}
          </Badge>
        );
      },
    },
  ];

  const handleCreateMenu = () => {
    console.log("Creating USSD menu:", { ussdCode, menuText, responseAction });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg">
            <Smartphone className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">USSD Services</h1>
            <p className="text-muted-foreground">Manage USSD codes and interactive menus</p>
          </div>
        </div>
        <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
          <Plus className="mr-2 h-4 w-4" />
          Request USSD Code
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Code className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Codes</p>
                <p className="text-2xl font-bold">5</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sessions</p>
                <p className="text-2xl font-bold">2,590</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Sessions</p>
                <p className="text-2xl font-bold">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">84.5%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="sessions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sessions">Live Sessions</TabsTrigger>
          <TabsTrigger value="menus">USSD Menus</TabsTrigger>
          <TabsTrigger value="create">Create Menu</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>USSD Sessions</CardTitle>
                  <CardDescription>Monitor active and recent USSD sessions</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={ussdSessions} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="menus" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>USSD Menus</CardTitle>
                  <CardDescription>Manage your USSD menu configurations</CardDescription>
                </div>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Menu Settings
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={menuColumns} data={ussdMenus} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create USSD Menu</CardTitle>
              <CardDescription>
                Design interactive USSD menus for your services
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="ussdCode">USSD Code</Label>
                  <Select value={ussdCode} onValueChange={setUssdCode}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select USSD code" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="*123#">*123#</SelectItem>
                      <SelectItem value="*456#">*456#</SelectItem>
                      <SelectItem value="*789#">*789#</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="responseAction">Response Action</Label>
                  <Select value={responseAction} onValueChange={setResponseAction}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select action" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="menu">Show Menu</SelectItem>
                      <SelectItem value="balance">Check Balance</SelectItem>
                      <SelectItem value="airtime">Buy Airtime</SelectItem>
                      <SelectItem value="custom">Custom Action</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="menuText">Menu Text</Label>
                <Textarea
                  id="menuText"
                  placeholder="Enter the menu text that users will see..."
                  value={menuText}
                  onChange={(e) => setMenuText(e.target.value)}
                  rows={6}
                />
                <p className="text-sm text-muted-foreground">
                  {menuText.length}/182 characters (USSD limit)
                </p>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Preview</h4>
                <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                  <div className="border-b border-green-400 pb-2 mb-2">
                    USSD Session: {ussdCode || "*CODE#"}
                  </div>
                  <div className="whitespace-pre-wrap">
                    {menuText || "Your menu text will appear here..."}
                  </div>
                  <div className="border-t border-green-400 pt-2 mt-2">
                    Reply to continue...
                  </div>
                </div>
              </div>
              
              <Button onClick={handleCreateMenu} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Create Menu
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Popular USSD Codes</CardTitle>
                <CardDescription>Most used codes this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { code: "*123#", usage: 1250, percentage: 48 },
                    { code: "*456#", usage: 890, percentage: 34 },
                    { code: "*789#", usage: 450, percentage: 18 },
                  ].map((item) => (
                    <div key={item.code} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="font-mono">
                          {item.code}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {item.usage.toLocaleString()} sessions
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-indigo-600 h-2 rounded-full" 
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Session Outcomes</CardTitle>
                <CardDescription>How sessions are completed</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { outcome: "Completed", count: 2190, color: "bg-green-500" },
                    { outcome: "Abandoned", count: 320, color: "bg-red-500" },
                    { outcome: "Timeout", count: 80, color: "bg-yellow-500" },
                  ].map((item) => (
                    <div key={item.outcome} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                        <span className="text-sm font-medium">{item.outcome}</span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {item.count.toLocaleString()}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default USSD;
