import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import SMSCampaignTable from "@/components/SMSCampaignTable";
import ComposeMessagePopup from "@/components/ComposeMessagePopup";

interface SMSCampaign {
  id: string;
  sender: string;
  campaignId: string;
  message: string;
  date: string;
  status: 'sent' | 'pending' | 'failed';
}

export default function SMSBulkOutbox() {
  const [isComposeOpen, setIsComposeOpen] = useState(false);
  const navigate = useNavigate();

  const handleCampaignClick = (campaign: SMSCampaign) => {
    navigate(`/campaign/${campaign.campaignId}`);
  };

  const handleComposeSend = (data: { from: string; to: string; message: string }) => {
    console.log("Sending SMS:", data);
    // Implement SMS sending logic here
  };

  return (
    <div className="space-y-6 bg-slate-900 min-h-screen p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Outbox</h1>
          <p className="text-gray-400">Manage your SMS campaigns and messages</p>
        </div>
        <Button
          onClick={() => setIsComposeOpen(true)}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Compose Message
        </Button>
      </div>

      {/* SMS Campaign Table */}
      <SMSCampaignTable
        onCampaignClick={handleCampaignClick}
        onComposeMessage={() => setIsComposeOpen(true)}
      />

      {/* Compose Message Popup */}
      <ComposeMessagePopup
        isOpen={isComposeOpen}
        onClose={() => setIsComposeOpen(false)}
        onSend={handleComposeSend}
      />
    </div>
  );
}