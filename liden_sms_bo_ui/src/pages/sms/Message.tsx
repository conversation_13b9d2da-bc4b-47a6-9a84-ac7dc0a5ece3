import { useState, useEffect } from "react"
import { Search, Calendar, Filter, Download, RefreshCw } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { LoadingSpinner } from "@/components/LoadingSpinner"
import { LidenAPI } from "@/lib/api-index"
import { format } from "date-fns"
import { DateRange } from "react-day-picker"

interface BulkMessage {
  count: string
  outbox_id: string
  msisdn: string
  short_code: string
  message: string
  message_length: string
  sms_cost: string
  alert_type: string
  network: string
  description: string
  created_at: string
}

interface BulkMessagesResponse {
  total_count: string
  data: BulkMessage[]
}

export default function Message() {
  const [messages, setMessages] = useState<BulkMessage[]>([])
  const [loading, setLoading] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState("")
  const [sortField, setSortField] = useState("created_at")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date()
  })
  const [networkFilter, setNetworkFilter] = useState<string>("")
  const [typeFilter, setTypeFilter] = useState<string>("")
  const [statusFilter, setStatusFilter] = useState<string>("")

  const fetchMessages = async () => {
    try {
      setLoading(true)
      const params = {
        limit: pageSize,
        offset: currentPage,
        start: dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
        end: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
        search: searchTerm || undefined,
        network: networkFilter || undefined,
        type: typeFilter || undefined,
        status: statusFilter || undefined,
        sort: sortField,
        order: sortOrder
      }

      const response = await LidenAPI.client.getBulkMessages(params)
      
      if (response.code === "Success" && response.data?.data) {
        const bulkData = response.data.data as BulkMessagesResponse
        setMessages(bulkData.data || [])
        setTotalCount(parseInt(bulkData.total_count || "0"))
      }
    } catch (error) {
      console.error("Failed to fetch messages:", error)
      setMessages([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMessages()
  }, [currentPage, pageSize, dateRange, networkFilter, typeFilter, statusFilter, sortField, sortOrder])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchMessages()
  }

  const handleRefresh = () => {
    fetchMessages()
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Export functionality to be implemented")
  }

  const getStatusBadge = (description: string) => {
    const status = description.toLowerCase()
    if (status.includes("delivered")) {
      return <Badge variant="default" className="bg-green-500">Delivered</Badge>
    } else if (status.includes("blacklist")) {
      return <Badge variant="destructive">Blacklisted</Badge>
    } else if (status.includes("failed")) {
      return <Badge variant="destructive">Failed</Badge>
    } else if (status.includes("pending")) {
      return <Badge variant="secondary">Pending</Badge>
    }
    return <Badge variant="outline">{description}</Badge>
  }

  const getNetworkBadge = (network: string) => {
    const networkColors: Record<string, string> = {
      'SAFARICOM': 'bg-green-600',
      'AIRTEL': 'bg-red-600',
      'TELKOM': 'bg-blue-600'
    }
    
    return (
      <Badge 
        variant="outline" 
        className={`${networkColors[network] || 'bg-gray-600'} text-white border-0`}
      >
        {network}
      </Badge>
    )
  }

  const totalPages = Math.ceil(totalCount / pageSize)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>SMS</span>
          <span>›</span>
          <span>Bulk SMS</span>
          <span>›</span>
          <span className="text-foreground">Message</span>
        </div>
      </div>

      {/* Filters Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Message Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <DatePickerWithRange
                date={dateRange}
                onDateChange={setDateRange}
                placeholder="Select date range"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Per page</label>
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Sort</label>
              <Select value={sortField} onValueChange={setSortField}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Date</SelectItem>
                  <SelectItem value="msisdn">Phone Number</SelectItem>
                  <SelectItem value="short_code">Sender ID</SelectItem>
                  <SelectItem value="network">Network</SelectItem>
                  <SelectItem value="sms_cost">Cost</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Network</label>
              <Select value={networkFilter} onValueChange={setNetworkFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Networks" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Networks</SelectItem>
                  <SelectItem value="SAFARICOM">Safaricom</SelectItem>
                  <SelectItem value="AIRTEL">Airtel</SelectItem>
                  <SelectItem value="TELKOM">Telkom</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Search and Actions */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Type to search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSearch} variant="default">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button onClick={handleRefresh} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button onClick={handleExport} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle>Messages ({totalCount.toLocaleString()})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">#ID</TableHead>
                      <TableHead>PHONE NUMBER</TableHead>
                      <TableHead>STATUS</TableHead>
                      <TableHead>TYPE</TableHead>
                      <TableHead>NETWORK</TableHead>
                      <TableHead>SENDER ID</TableHead>
                      <TableHead>MESSAGE</TableHead>
                      <TableHead>COST</TableHead>
                      <TableHead>SENT ON</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {messages.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                          No messages found
                        </TableCell>
                      </TableRow>
                    ) : (
                      messages.map((message) => (
                        <TableRow key={message.outbox_id}>
                          <TableCell className="font-mono text-sm">
                            {message.outbox_id}
                          </TableCell>
                          <TableCell className="font-mono">
                            {message.msisdn}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(message.description)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {message.alert_type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {getNetworkBadge(message.network)}
                          </TableCell>
                          <TableCell className="font-medium">
                            {message.short_code}
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate" title={message.message}>
                              {message.message}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {parseFloat(message.sms_cost).toFixed(1)}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {format(new Date(message.created_at), 'yyyy-MM-dd HH:mm:ss')}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} entries
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                      
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1
                        return (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      })}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
