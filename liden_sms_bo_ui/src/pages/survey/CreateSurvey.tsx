import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Plus, 
  Trash2, 
  Save, 
  Send,
  Eye,
  Settings,
  MessageSquare,
  Users,
  Calendar,
  Clock
} from "lucide-react";

interface Question {
  id: string;
  type: 'text' | 'rating' | 'multiple-choice' | 'yes-no';
  question: string;
  options?: string[];
  required: boolean;
}

const CreateSurvey = () => {
  const [surveyTitle, setSurveyTitle] = useState("");
  const [surveyDescription, setSurveyDescription] = useState("");
  const [questions, setQuestions] = useState<Question[]>([]);
  const [targetAudience, setTargetAudience] = useState("");
  const [scheduledDate, setScheduledDate] = useState("");
  const [isAnonymous, setIsAnonymous] = useState(true);
  const [allowMultipleResponses, setAllowMultipleResponses] = useState(false);

  const addQuestion = (type: Question['type']) => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      type,
      question: "",
      options: type === 'multiple-choice' ? ["", ""] : undefined,
      required: false,
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (id: string, updates: Partial<Question>) => {
    setQuestions(questions.map(q => q.id === id ? { ...q, ...updates } : q));
  };

  const removeQuestion = (id: string) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  const addOption = (questionId: string) => {
    const question = questions.find(q => q.id === questionId);
    if (question && question.options) {
      updateQuestion(questionId, {
        options: [...question.options, ""]
      });
    }
  };

  const updateOption = (questionId: string, optionIndex: number, value: string) => {
    const question = questions.find(q => q.id === questionId);
    if (question && question.options) {
      const newOptions = [...question.options];
      newOptions[optionIndex] = value;
      updateQuestion(questionId, { options: newOptions });
    }
  };

  const removeOption = (questionId: string, optionIndex: number) => {
    const question = questions.find(q => q.id === questionId);
    if (question && question.options && question.options.length > 2) {
      const newOptions = question.options.filter((_, index) => index !== optionIndex);
      updateQuestion(questionId, { options: newOptions });
    }
  };

  const handleSaveDraft = () => {
    console.log("Saving draft:", { surveyTitle, surveyDescription, questions });
  };

  const handlePublishSurvey = () => {
    console.log("Publishing survey:", { 
      surveyTitle, 
      surveyDescription, 
      questions, 
      targetAudience,
      scheduledDate,
      isAnonymous,
      allowMultipleResponses
    });
  };

  const renderQuestionEditor = (question: Question) => {
    return (
      <Card key={question.id} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Badge variant="outline">
              {question.type.replace('-', ' ').toUpperCase()}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeQuestion(question.id)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Question</Label>
            <Input
              placeholder="Enter your question..."
              value={question.question}
              onChange={(e) => updateQuestion(question.id, { question: e.target.value })}
            />
          </div>

          {question.type === 'multiple-choice' && (
            <div className="space-y-2">
              <Label>Options</Label>
              {question.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    placeholder={`Option ${index + 1}`}
                    value={option}
                    onChange={(e) => updateOption(question.id, index, e.target.value)}
                  />
                  {question.options && question.options.length > 2 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeOption(question.id, index)}
                      className="text-red-500"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => addOption(question.id)}
                className="mt-2"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Option
              </Button>
            </div>
          )}

          {question.type === 'rating' && (
            <div className="space-y-2">
              <Label>Rating Scale</Label>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span>1 (Poor)</span>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((num) => (
                    <div key={num} className="w-8 h-8 border rounded flex items-center justify-center">
                      {num}
                    </div>
                  ))}
                </div>
                <span>5 (Excellent)</span>
              </div>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id={`required-${question.id}`}
              checked={question.required}
              onCheckedChange={(checked) => updateQuestion(question.id, { required: !!checked })}
            />
            <Label htmlFor={`required-${question.id}`} className="text-sm">
              Required question
            </Label>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <MessageSquare className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Create Survey</h1>
            <p className="text-muted-foreground">Design and deploy SMS surveys to collect feedback</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleSaveDraft}>
            <Save className="mr-2 h-4 w-4" />
            Save Draft
          </Button>
          <Button onClick={handlePublishSurvey}>
            <Send className="mr-2 h-4 w-4" />
            Publish Survey
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Survey Details */}
          <Card>
            <CardHeader>
              <CardTitle>Survey Details</CardTitle>
              <CardDescription>Basic information about your survey</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Survey Title</Label>
                <Input
                  id="title"
                  placeholder="Enter survey title..."
                  value={surveyTitle}
                  onChange={(e) => setSurveyTitle(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the purpose of your survey..."
                  value={surveyDescription}
                  onChange={(e) => setSurveyDescription(e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Questions */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Questions</CardTitle>
                  <CardDescription>Add questions to your survey</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Select onValueChange={(value) => addQuestion(value as Question['type'])}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Add question type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">Text Response</SelectItem>
                      <SelectItem value="rating">Rating Scale</SelectItem>
                      <SelectItem value="multiple-choice">Multiple Choice</SelectItem>
                      <SelectItem value="yes-no">Yes/No</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {questions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No questions added yet</p>
                  <p className="text-sm">Use the dropdown above to add your first question</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map(renderQuestionEditor)}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Survey Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="audience">Target Audience</Label>
                <Select value={targetAudience} onValueChange={setTargetAudience}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select audience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Contacts</SelectItem>
                    <SelectItem value="customers">Customers Only</SelectItem>
                    <SelectItem value="prospects">Prospects Only</SelectItem>
                    <SelectItem value="custom">Custom List</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="schedule">Schedule</Label>
                <Input
                  id="schedule"
                  type="datetime-local"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="anonymous" className="text-sm">Anonymous responses</Label>
                  <Switch
                    id="anonymous"
                    checked={isAnonymous}
                    onCheckedChange={setIsAnonymous}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="multiple" className="text-sm">Allow multiple responses</Label>
                  <Switch
                    id="multiple"
                    checked={allowMultipleResponses}
                    onCheckedChange={setAllowMultipleResponses}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Preview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-3 bg-muted rounded-lg">
                  <h4 className="font-medium">{surveyTitle || "Survey Title"}</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {surveyDescription || "Survey description will appear here..."}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <MessageSquare className="h-4 w-4" />
                    <span>{questions.length} questions</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Users className="h-4 w-4" />
                    <span>{targetAudience || "No audience selected"}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-4 w-4" />
                    <span>{scheduledDate ? new Date(scheduledDate).toLocaleDateString() : "Not scheduled"}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Survey Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Estimated Reach</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total contacts</span>
                  <span className="font-medium">2,450</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Estimated cost</span>
                  <span className="font-medium">$36.75</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Expected responses</span>
                  <span className="font-medium">~1,960 (80%)</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CreateSurvey;
