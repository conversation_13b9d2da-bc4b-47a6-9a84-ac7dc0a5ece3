import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  Settings, 
  Plus, 
  Clock,
  CheckCircle,
  XCircle,
  FileText,
  Send,
  Filter
} from "lucide-react";

const ServicesRequest = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [serviceType, setServiceType] = useState("");
  const [requestTitle, setRequestTitle] = useState("");
  const [requestDescription, setRequestDescription] = useState("");

  // Mock data for service requests
  const serviceRequests = [
    {
      id: "SR-001",
      title: "Premium SMS Sender ID",
      type: "Sender ID",
      status: "Approved",
      priority: "High",
      submittedDate: "2024-01-10",
      resolvedDate: "2024-01-12",
    },
    {
      id: "SR-002",
      title: "USSD Code Request",
      type: "USSD",
      status: "Pending",
      priority: "Medium",
      submittedDate: "2024-01-14",
      resolvedDate: null,
    },
    {
      id: "SR-003",
      title: "Shortcode Registration",
      type: "Shortcode",
      status: "In Review",
      priority: "High",
      submittedDate: "2024-01-15",
      resolvedDate: null,
    },
  ];

  const columns = [
    {
      accessorKey: "id",
      header: "Request ID",
      cell: ({ row }: any) => (
        <span className="font-mono">{row.getValue("id")}</span>
      ),
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }: any) => (
        <span className="font-medium">{row.getValue("title")}</span>
      ),
    },
    {
      accessorKey: "type",
      header: "Service Type",
      cell: ({ row }: any) => (
        <Badge variant="outline">{row.getValue("type")}</Badge>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        const variants = {
          Approved: "default",
          Pending: "secondary",
          "In Review": "outline",
          Rejected: "destructive",
        };
        return (
          <Badge variant={variants[status as keyof typeof variants] as any}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }: any) => {
        const priority = row.getValue("priority");
        const colors = {
          High: "text-red-600",
          Medium: "text-yellow-600",
          Low: "text-green-600",
        };
        return (
          <span className={colors[priority as keyof typeof colors]}>
            {priority}
          </span>
        );
      },
    },
    {
      accessorKey: "submittedDate",
      header: "Submitted",
    },
  ];

  const handleSubmitRequest = () => {
    if (!serviceType || !requestTitle || !requestDescription) {
      alert("Please fill in all fields");
      return;
    }
    console.log("Submitting request:", { serviceType, requestTitle, requestDescription });
    // Reset form
    setServiceType("");
    setRequestTitle("");
    setRequestDescription("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Settings className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Services Request</h1>
            <p className="text-muted-foreground">Request new services and track existing requests</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                <p className="text-2xl font-bold">8</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                <p className="text-2xl font-bold">1</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="requests" className="space-y-4">
        <TabsList>
          <TabsTrigger value="requests">My Requests</TabsTrigger>
          <TabsTrigger value="new">New Request</TabsTrigger>
          <TabsTrigger value="templates">Request Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Service Requests</CardTitle>
                  <CardDescription>Track the status of your service requests</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={serviceRequests} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="new" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Submit New Request</CardTitle>
              <CardDescription>Request new services or features</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="serviceType">Service Type</Label>
                  <Select value={serviceType} onValueChange={setServiceType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select service type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sender-id">Sender ID</SelectItem>
                      <SelectItem value="shortcode">Shortcode</SelectItem>
                      <SelectItem value="ussd">USSD Code</SelectItem>
                      <SelectItem value="api">API Access</SelectItem>
                      <SelectItem value="integration">Integration Support</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="title">Request Title</Label>
                <Input
                  id="title"
                  placeholder="Brief description of your request"
                  value={requestTitle}
                  onChange={(e) => setRequestTitle(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Detailed Description</Label>
                <Textarea
                  id="description"
                  placeholder="Provide detailed information about your request..."
                  value={requestDescription}
                  onChange={(e) => setRequestDescription(e.target.value)}
                  rows={6}
                />
              </div>
              <Button onClick={handleSubmitRequest} className="w-full">
                <Send className="mr-2 h-4 w-4" />
                Submit Request
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                title: "Sender ID Request",
                description: "Request a custom alphanumeric sender ID",
                type: "sender-id"
              },
              {
                title: "Shortcode Registration",
                description: "Register a new shortcode for SMS services",
                type: "shortcode"
              },
              {
                title: "USSD Code Request",
                description: "Request a USSD code for interactive services",
                type: "ussd"
              },
              {
                title: "API Integration",
                description: "Request API access and integration support",
                type: "api"
              },
              {
                title: "Technical Support",
                description: "Get technical assistance and support",
                type: "support"
              },
              {
                title: "Custom Service",
                description: "Request a custom service or feature",
                type: "custom"
              },
            ].map((template, index) => (
              <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">{template.title}</CardTitle>
                  <CardDescription>{template.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ServicesRequest;
