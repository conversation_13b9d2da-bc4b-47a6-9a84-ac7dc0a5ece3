import { useState } from "react"
import { BarChart3, Users, MessageSquare, TestTube, LogIn, LogOut, Key } from "lucide-react"
import { StatCard } from "@/components/StatCard"
import { MetricCard } from "@/components/MetricCard"
import { DeliveryStats } from "@/components/DeliveryStats"
import { ComprehensiveDashboard } from "@/components/ComprehensiveDashboard"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ClientAPI } from "@/lib/api-extended"

export default function Dashboard() {
  const [viewMode, setViewMode] = useState<'legacy' | 'comprehensive'>('comprehensive')

  const testApiCall = async () => {
    try {
      console.log('Testing API call...')
      const response = await ClientAPI.getWalletInfo('46')
      console.log('API Response:', response)
      alert('API call successful! Check console for details.')
    } catch (error) {
      console.error('API Error:', error)
      alert('API call failed! Check console for details.')
    }
  }

  const testLogin = async () => {
    try {
      console.log('Testing login...')
      const { LidenAPI } = await import('@/lib/api-index')
      const response = await LidenAPI.auth.login({
        userName: "704050143",
        countryCode: "254",
        password: "Keny@1-Nb1",
        apigw: "WEB_GW"
      })
      console.log('Login Response:', response)

      if (response.success && response.data?.data?.token) {
        const token = response.data.data.token
        localStorage.setItem('auth_token', token)
        const { apiService } = await import('@/lib/api-index')
        apiService.setAuthToken(token)
        alert('Login successful! Token stored. Try the API call now.')
      } else {
        alert('Login failed! Check console for details.')
      }
    } catch (error) {
      console.error('Login Error:', error)
      alert('Login failed! Check console for details.')
    }
  }

  const clearToken = () => {
    const { CookieManager } = require('@/lib/utils')
    CookieManager.clearAuthData()
    alert('All authentication data cleared! You can test login again.')
  }

  const setTestToken = async () => {
    const { CookieManager } = await import('@/lib/utils')
    const testToken = 'MWlmbkdobGFqSVFIbHp5bTNiVVIySCs4Ukp6VTlMQk9CdHZCalFJcTFiTnVia2RZRFZJZjVLeXM0ZlhFRVJtem5JdTI2bTlweUlrbzVzWVJPUklGTGdXT2duNHFPaFhUQmpDb1VybGhtemNjKy9tZVNob0Q5Z2NvUTdzOTB4NTc5M0UxbUtYMVVkN1pKWk5SWTFMc3kwVUc5c0xxY2lyZnVnVVBiZjRPcDJRQVVMUXR2V0JjMGovMm1KOE5Sby8wYXJWODkweGFFdkhmTkZvN2FvYXNxQldrYk9hQmdkemp4cjM4ZUVZa2YzVTFiSU44Qld3QUl0K05FL0hTVmJWTU5jNWsrd0JqUzMzYnV3UFJmMjlLK2hWY1YySVhFTUpobXc2d2tKQUk1UzZSOHYxSFNLNEtTREo5MTR6ZVlLOFcvL2NFWXEvNWpoVEdEQVl6dGNnd3R3Zi9Jb3RFNGZ0bGhFLy9mc2JkRmFjPTo6tS612tZcn782bctVfiwvwQ=='

    const tokenData = {
      token: testToken,
      clientData: '',
      expires: 1,
      type: 'hour' as const,
      issuedAt: Date.now()
    }

    CookieManager.setAuthToken(tokenData)
    const { apiService } = await import('@/lib/api-index')
    apiService.setAuthToken(testToken)
    console.log('Test token set in secure cookies:', testToken)
    alert('Test token set! Try the API call now.')
  }

  const testWalletAPI = async () => {
    try {
      const { apiService } = await import('@/lib/api-index')
      console.log('Testing wallet API...')
      const response = await apiService.get('/account/v1/view/wallet', { clientId: '46' })
      console.log('Wallet API Response:', response)
      alert(`Wallet API Response: ${JSON.stringify(response, null, 2)}`)
    } catch (error) {
      console.error('Wallet API Error:', error)
      alert(`Wallet API Error: ${error}`)
    }
  }

  const checkTokenStatus = async () => {
    const { CookieManager } = await import('@/lib/utils')
    const token = CookieManager.getAuthToken()
    const isValid = CookieManager.isTokenValid()
    const expirationInfo = CookieManager.getTokenExpirationInfo()

    const status = {
      hasToken: !!token,
      isValid,
      expirationInfo,
      tokenPreview: token ? `${token.substring(0, 50)}...` : 'No token'
    }

    console.log('Token Status:', status)
    alert(`Token Status:\n${JSON.stringify(status, null, 2)}`)
  }

  const runAPITests = async () => {
    try {
      const { runAllTests } = await import('@/lib/api-test')
      console.log('Running comprehensive API tests...')
      await runAllTests()
      alert('API tests completed! Check console for detailed results.')
    } catch (error) {
      console.error('Test execution error:', error)
      alert(`Test execution error: ${error}`)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Home</span>
          <span>›</span>
          <span className="text-foreground">Dashboard</span>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={testLogin}
            className="mr-2"
          >
            <LogIn className="w-4 h-4 mr-2" />
            Test Login
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={testApiCall}
            className="mr-2"
          >
            <TestTube className="w-4 h-4 mr-2" />
            Test API
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={setTestToken}
            className="mr-2"
          >
            <Key className="w-4 h-4 mr-2" />
            Set Test Token
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={clearToken}
            className="mr-2"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Clear Token
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={testWalletAPI}
            className="mr-2"
          >
            <TestTube className="w-4 h-4 mr-2" />
            Test Wallet API
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={checkTokenStatus}
            className="mr-2"
          >
            <Key className="w-4 h-4 mr-2" />
            Check Token
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={runAPITests}
            className="mr-2"
          >
            <TestTube className="w-4 h-4 mr-2" />
            Run Tests
          </Button>
          <Button
            variant={viewMode === 'legacy' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('legacy')}
          >
            Legacy View
          </Button>
          <Button
            variant={viewMode === 'comprehensive' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('comprehensive')}
          >
            Enhanced View
          </Button>
        </div>
      </div>

      {/* Dashboard Content */}
      {viewMode === 'comprehensive' ? (
        <ComprehensiveDashboard clientId="46" />
      ) : (
        <div className="space-y-6">
          {/* Main Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Account Balance */}
            <StatCard
              title="Account Balance"
              value="KES -45,533,412"
              subtitle="KES 257,579"
              bonus="KES 0"
              icon={BarChart3}
              iconClassName="bg-blue-500"
              actions={[
                { label: "View SMS Units", variant: "outline" },
                { label: "Edit Threshold", variant: "default" }
              ]}
            />

            {/* Total Contacts */}
            <MetricCard
              title="Total Contacts"
              value="0"
              subtitle=""
              bonus=""
              icon={Users}
              iconColor="text-red-500"
            />

            {/* Total Messages Sent */}
            <MetricCard
              title="Total Message Sent"
              value="2"
              subtitle=""
              bonus=""
              icon={MessageSquare}
              iconColor="text-orange-500"
            />
          </div>

          {/* Second Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Delivery Stats */}
            <DeliveryStats />

            {/* Chart Placeholder */}
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-foreground">Delivery Chart</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-green-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium">Chart Area</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}