import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/DataTable";
import { DateRangePicker } from "@/components/DateRangePicker";
import { 
  DollarSign, 
  Send, 
  CheckCircle, 
  XCircle, 
  Clock,
  Users,
  TrendingUp,
  Filter,
  Smartphone,
  CreditCard,
  History
} from "lucide-react";

const Airtime = () => {
  const [selectedDateRange, setSelectedDateRange] = useState<any>(undefined);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [amount, setAmount] = useState("");
  const [network, setNetwork] = useState("");

  // Mock data for airtime transactions
  const airtimeTransactions = [
    {
      id: "1",
      phoneNumber: "+254704050143",
      amount: "KES 100",
      network: "Safaricom",
      status: "Completed",
      reference: "AT001234567",
      timestamp: "2024-01-16 14:30",
    },
    {
      id: "2",
      phoneNumber: "+254712345678",
      amount: "KES 50",
      network: "Airtel",
      status: "Completed",
      reference: "AT001234568",
      timestamp: "2024-01-16 13:45",
    },
    {
      id: "3",
      phoneNumber: "+254798765432",
      amount: "KES 200",
      network: "Telkom",
      status: "Failed",
      reference: "AT001234569",
      timestamp: "2024-01-16 12:20",
    },
    {
      id: "4",
      phoneNumber: "+254723456789",
      amount: "KES 500",
      network: "Safaricom",
      status: "Pending",
      reference: "AT001234570",
      timestamp: "2024-01-16 14:35",
    },
  ];

  const columns = [
    {
      accessorKey: "phoneNumber",
      header: "Phone Number",
      cell: ({ row }: any) => (
        <span className="font-mono">{row.getValue("phoneNumber")}</span>
      ),
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }: any) => (
        <span className="font-medium text-green-600">{row.getValue("amount")}</span>
      ),
    },
    {
      accessorKey: "network",
      header: "Network",
      cell: ({ row }: any) => {
        const network = row.getValue("network");
        const colors = {
          Safaricom: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          Airtel: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          Telkom: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
        };
        return (
          <Badge variant="secondary" className={colors[network as keyof typeof colors]}>
            {network}
          </Badge>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }: any) => {
        const status = row.getValue("status");
        const icons = {
          Completed: <CheckCircle className="h-4 w-4 text-green-500" />,
          Pending: <Clock className="h-4 w-4 text-yellow-500" />,
          Failed: <XCircle className="h-4 w-4 text-red-500" />,
        };
        return (
          <div className="flex items-center space-x-2">
            {icons[status as keyof typeof icons]}
            <span>{status}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "reference",
      header: "Reference",
      cell: ({ row }: any) => (
        <span className="font-mono text-sm">{row.getValue("reference")}</span>
      ),
    },
    {
      accessorKey: "timestamp",
      header: "Date",
      cell: ({ row }: any) => (
        <span className="text-sm text-muted-foreground">{row.getValue("timestamp")}</span>
      ),
    },
  ];

  const handleSendAirtime = () => {
    if (!phoneNumber || !amount || !network) {
      alert("Please fill in all fields");
      return;
    }
    console.log("Sending airtime:", { phoneNumber, amount, network });
    // Reset form
    setPhoneNumber("");
    setAmount("");
    setNetwork("");
  };

  // Quick amount buttons
  const quickAmounts = [50, 100, 200, 500, 1000];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Airtime Services</h1>
            <p className="text-muted-foreground">Send airtime to any mobile number</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <CreditCard className="mr-2 h-4 w-4" />
            Top Up Wallet
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Send className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sent</p>
                <p className="text-2xl font-bold">KES 45,200</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Successful</p>
                <p className="text-2xl font-bold">342</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Recipients</p>
                <p className="text-2xl font-bold">298</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">96.8%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="send" className="space-y-4">
        <TabsList>
          <TabsTrigger value="send">Send Airtime</TabsTrigger>
          <TabsTrigger value="history">Transaction History</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Airtime</TabsTrigger>
          <TabsTrigger value="wallet">Wallet</TabsTrigger>
        </TabsList>

        <TabsContent value="send" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Send Airtime</CardTitle>
                <CardDescription>
                  Send airtime credit to any mobile number
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    placeholder="e.g., +254704050143"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="network">Network</Label>
                  <Select value={network} onValueChange={setNetwork}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select network" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Safaricom">Safaricom</SelectItem>
                      <SelectItem value="Airtel">Airtel</SelectItem>
                      <SelectItem value="Telkom">Telkom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amount">Amount (KES)</Label>
                  <Input
                    id="amount"
                    type="number"
                    placeholder="Enter amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Quick Amounts</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {quickAmounts.map((quickAmount) => (
                      <Button
                        key={quickAmount}
                        variant="outline"
                        size="sm"
                        onClick={() => setAmount(quickAmount.toString())}
                        className="text-xs"
                      >
                        {quickAmount}
                      </Button>
                    ))}
                  </div>
                </div>

                <Button onClick={handleSendAirtime} className="w-full">
                  <Send className="mr-2 h-4 w-4" />
                  Send Airtime
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Network Information</CardTitle>
                <CardDescription>Available networks and rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { 
                      name: "Safaricom", 
                      rate: "2.5%", 
                      minAmount: "KES 10", 
                      maxAmount: "KES 10,000",
                      status: "Available"
                    },
                    { 
                      name: "Airtel", 
                      rate: "2.8%", 
                      minAmount: "KES 10", 
                      maxAmount: "KES 5,000",
                      status: "Available"
                    },
                    { 
                      name: "Telkom", 
                      rate: "3.0%", 
                      minAmount: "KES 10", 
                      maxAmount: "KES 3,000",
                      status: "Available"
                    },
                  ].map((networkInfo) => (
                    <Card key={networkInfo.name}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{networkInfo.name}</h4>
                          <Badge variant="outline" className="text-green-600">
                            {networkInfo.status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                          <div>Rate: {networkInfo.rate}</div>
                          <div>Min: {networkInfo.minAmount}</div>
                          <div>Max: {networkInfo.maxAmount}</div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Transaction History</CardTitle>
                  <CardDescription>View all airtime transactions</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={setSelectedDateRange}
                    placeholder="Select date range"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable columns={columns} data={airtimeTransactions} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Airtime</CardTitle>
              <CardDescription>Send airtime to multiple recipients at once</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8 text-muted-foreground">
                <Smartphone className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Bulk airtime feature coming soon</p>
                <p className="text-sm">Upload CSV files to send airtime to multiple recipients</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="wallet" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Wallet Balance</CardTitle>
                <CardDescription>Your current airtime wallet balance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6">
                  <div className="text-4xl font-bold text-green-600 mb-2">KES 2,450.00</div>
                  <p className="text-muted-foreground">Available Balance</p>
                  <Button className="mt-4">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Top Up Wallet
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Wallet Activity</CardTitle>
                <CardDescription>Latest wallet transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { type: "Top Up", amount: "+KES 5,000", date: "2024-01-15" },
                    { type: "Airtime Sent", amount: "-KES 100", date: "2024-01-16" },
                    { type: "Airtime Sent", amount: "-KES 50", date: "2024-01-16" },
                    { type: "Airtime Sent", amount: "-KES 200", date: "2024-01-16" },
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b">
                      <div>
                        <p className="text-sm font-medium">{activity.type}</p>
                        <p className="text-xs text-muted-foreground">{activity.date}</p>
                      </div>
                      <span className={`font-medium ${
                        activity.amount.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {activity.amount}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Airtime;
