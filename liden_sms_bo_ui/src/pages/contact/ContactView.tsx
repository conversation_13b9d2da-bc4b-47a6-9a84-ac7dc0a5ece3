import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UserPlus, Upload, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/DateRangePicker"
import { DataTable, Column } from "@/components/DataTable"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Sample data
const contacts = [
  {
    id: 1,
    name: "<PERSON>",
    phone: "+1234567890",
    email: "<EMAIL>",
    status: "Active",
    group: "Customers",
    created: "2024-01-15",
  },
  {
    id: 2,
    name: "<PERSON>", 
    phone: "+1234567891",
    email: "<EMAIL>",
    status: "Inactive",
    group: "Prospects",
    created: "2024-01-20",
  },
  {
    id: 3,
    name: "<PERSON>",
    phone: "+1234567892", 
    email: "<EMAIL>",
    status: "Active",
    group: "Customers",
    created: "2024-02-01",
  },
  {
    id: 4,
    name: "<PERSON> <PERSON>",
    phone: "+1234567893",
    email: "<EMAIL>", 
    status: "Pending",
    group: "Leads",
    created: "2024-02-10",
  },
  {
    id: 5,
    name: "David Brown",
    phone: "+1234567894",
    email: "<EMAIL>",
    status: "Active", 
    group: "Customers",
    created: "2024-02-15",
  },
]

const columns: Column[] = [
  {
    key: "name",
    label: "Name",
    sortable: true,
  },
  {
    key: "phone", 
    label: "Phone",
    sortable: true,
  },
  {
    key: "email",
    label: "Email",
    sortable: true,
  },
  {
    key: "status",
    label: "Status",
    render: (value) => (
      <Badge
        variant={
          value === "Active" ? "default" :
          value === "Inactive" ? "destructive" :
          "secondary"
        }
      >
        {value}
      </Badge>
    ),
  },
  {
    key: "group",
    label: "Group",
    sortable: true,
  },
  {
    key: "created",
    label: "Created",
    sortable: true,
  },
  {
    key: "actions",
    label: "Actions",
    render: (_, row) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>Edit</DropdownMenuItem>
          <DropdownMenuItem>Delete</DropdownMenuItem>
          <DropdownMenuItem>View Details</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
]

export function ContactView() {
  const [dateRange, setDateRange] = useState<any>(undefined)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [groupFilter, setGroupFilter] = useState<string>("all")

  const handleSearch = (query: string) => {
    console.log("Search:", query)
  }

  const handleExport = (format: string) => {
    console.log("Export:", format)
  }

  const mobileCardRender = (contact: any) => (
    <div className="space-y-3 p-1">
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0">
          <div className="grid grid-cols-1 gap-2 text-sm">
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Partner Name</span>
              <span className="font-medium text-foreground text-right">{contact.name}</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Email/Phone</span>
              <div className="text-right">
                <div className="text-foreground">{contact.email}</div>
                <div className="text-muted-foreground text-xs">{contact.phone}</div>
              </div>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Address</span>
              <span className="text-foreground text-right">-</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Country</span>
              <span className="text-foreground text-right">-</span>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Status</span>
              <Badge
                variant={
                  contact.status === "Active" ? "default" :
                  contact.status === "Inactive" ? "destructive" :
                  "secondary"
                }
                className="ml-auto"
              >
                {contact.status}
              </Badge>
            </div>
            <div className="flex justify-between items-center border-b border-border pb-2">
              <span className="font-medium text-muted-foreground">Created</span>
              <span className="text-foreground text-right">{contact.created}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium text-muted-foreground">Updated</span>
              <span className="text-foreground text-right">{contact.created}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-center pt-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="w-8 h-8 rounded-full">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Edit</DropdownMenuItem>
            <DropdownMenuItem>Delete</DropdownMenuItem>
            <DropdownMenuItem>View Details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )

  const filters = (
    <>
      <DateRangePicker
        value={dateRange}
        onChange={setDateRange}
        placeholder="Select date range"
        className="w-full sm:w-auto"
      />
      
      <Select value={statusFilter} onValueChange={setStatusFilter}>
        <SelectTrigger className="w-full sm:w-32 h-8">
          <SelectValue placeholder="Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Status</SelectItem>
          <SelectItem value="active">Active</SelectItem>
          <SelectItem value="inactive">Inactive</SelectItem>
          <SelectItem value="pending">Pending</SelectItem>
        </SelectContent>
      </Select>

      <Select value={groupFilter} onValueChange={setGroupFilter}>
        <SelectTrigger className="w-full sm:w-32 h-8">
          <SelectValue placeholder="Group" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Groups</SelectItem>
          <SelectItem value="customers">Customers</SelectItem>
          <SelectItem value="prospects">Prospects</SelectItem>
          <SelectItem value="leads">Leads</SelectItem>
        </SelectContent>
      </Select>
    </>
  )

  const actions = (
    <>
      <Button size="sm" className="h-8">
        <UserPlus className="mr-2 h-4 w-4" />
        Add Contact
      </Button>
      <Button variant="outline" size="sm" className="h-8">
        <Upload className="mr-2 h-4 w-4" />
        Import
      </Button>
    </>
  )

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Contact Management</h1>
          <p className="text-muted-foreground">Manage your contacts and contact groups</p>
        </div>
      </div>

      <DataTable
        data={contacts}
        columns={columns}
        title="Contacts"
        searchable={true}
        searchPlaceholder="Search contacts..."
        filters={filters}
        actions={actions}
        onSearch={handleSearch}
        onExport={handleExport}
        mobileCardRender={mobileCardRender}
        className="bg-card border rounded-lg p-6"
      />
    </div>
  )
}