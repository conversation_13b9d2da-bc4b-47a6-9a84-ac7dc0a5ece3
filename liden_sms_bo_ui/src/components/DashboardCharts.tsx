import { useState, useEffect } from "react"
import { 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell, 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from "recharts"
import { <PERSON>ren<PERSON>Up, <PERSON><PERSON><PERSON> as PieChartIcon, BarChart3, RefreshCw, AlertCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { ClientAPI } from "@/lib/api-extended"
import { DashboardStatsResponse, DashboardStatsParams, WalletResponse } from "@/lib/api-types"

interface DashboardChartsProps {
  clientId?: string
  className?: string
  dateRange?: {
    start: string
    end: string
  }
  onRefresh?: () => void
}

export function DashboardCharts({ 
  clientId, 
  className,
  dateRange,
  onRefresh 
}: DashboardChartsProps) {
  const [chartsData, setChartsData] = useState<DashboardStatsResponse | null>(null)
  const [walletData, setWalletData] = useState<WalletResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  const fetchChartsData = async () => {
    try {
      setError(null)
      const params: DashboardStatsParams = {
        clientId,
        start: dateRange?.start || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: dateRange?.end || new Date().toISOString().split('T')[0],
      }
      
      const [statsResponse, walletResponse] = await Promise.all([
        ClientAPI.getDashboardStatistics(params),
        ClientAPI.getWalletInfo(clientId)
      ])
      
      if (statsResponse.success && statsResponse.data) {
        setChartsData(statsResponse.data)
      }
      
      if (walletResponse.success && walletResponse.data) {
        setWalletData(walletResponse.data)
      }
      
      if (!statsResponse.success) {
        setError(statsResponse.message || 'Failed to fetch chart data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchChartsData()
    onRefresh?.()
  }

  useEffect(() => {
    fetchChartsData()
  }, [clientId, dateRange])

  // Prepare data for charts
  const deliveryPieData = chartsData ? [
    { name: 'Delivered', value: chartsData.totalMessagesDelivered, color: '#10B981' },
    { name: 'Failed', value: chartsData.totalMessagesFailed, color: '#EF4444' },
    { name: 'Pending', value: chartsData.totalMessagesPending, color: '#F59E0B' },
  ] : []

  const dailyTrendsData = chartsData?.dailyStats.map(stat => ({
    date: new Date(stat.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    sent: stat.sent,
    delivered: stat.delivered,
    failed: stat.failed,
    pending: stat.pending,
    cost: stat.cost,
  })) || []

  const costAnalysisData = chartsData?.dailyStats.map(stat => ({
    date: new Date(stat.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    cost: stat.cost,
    messages: stat.sent,
    costPerMessage: stat.sent > 0 ? stat.cost / stat.sent : 0,
  })) || []

  const balanceData = walletData ? [
    { name: 'Current Balance', value: walletData.balance, color: '#3B82F6' },
    { name: 'Remaining to Threshold', value: Math.max(0, walletData.alertThreshold - walletData.balance), color: '#E5E7EB' },
  ] : []

  const COLORS = ['#10B981', '#EF4444', '#F59E0B', '#3B82F6', '#8B5CF6']

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <Card className={cn("bg-card border-border", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-64 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn("bg-card border-border border-red-200", className)}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">Error loading chart data</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="mt-3"
            disabled={refreshing}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("bg-card border-border transition-all duration-200 hover:shadow-lg", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            <span>Analytics Charts</span>
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="delivery" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="delivery">Delivery</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="costs">Costs</TabsTrigger>
            <TabsTrigger value="balance">Balance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="delivery" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground flex items-center space-x-2">
                <PieChartIcon className="h-4 w-4" />
                <span>Delivery Success Rate</span>
              </h4>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={deliveryPieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {deliveryPieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="trends" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground flex items-center space-x-2">
                <TrendingUp className="h-4 w-4" />
                <span>SMS Usage Trends Over Time</span>
              </h4>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyTrendsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Line type="monotone" dataKey="sent" stroke="#3B82F6" strokeWidth={2} name="Sent" />
                    <Line type="monotone" dataKey="delivered" stroke="#10B981" strokeWidth={2} name="Delivered" />
                    <Line type="monotone" dataKey="failed" stroke="#EF4444" strokeWidth={2} name="Failed" />
                    <Line type="monotone" dataKey="pending" stroke="#F59E0B" strokeWidth={2} name="Pending" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="costs" className="mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">Cost Analysis</h4>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={costAnalysisData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar yAxisId="left" dataKey="cost" fill="#8B5CF6" name="Total Cost" />
                    <Line yAxisId="right" type="monotone" dataKey="costPerMessage" stroke="#F59E0B" strokeWidth={2} name="Cost per Message" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="balance" className="mt-4">
            {walletData ? (
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-muted-foreground">Balance vs Threshold</h4>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={balanceData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value.toLocaleString()}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {balanceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="text-center text-sm text-muted-foreground">
                  Current balance: {walletData.balance.toLocaleString()} {walletData.currency} | 
                  Alert threshold: {walletData.alertThreshold.toLocaleString()} {walletData.currency}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <PieChartIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No wallet data available</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
