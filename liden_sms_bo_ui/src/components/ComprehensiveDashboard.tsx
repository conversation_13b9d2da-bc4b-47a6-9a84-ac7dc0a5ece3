import { useState, useEffect } from "react"
import { Calendar, RefreshCw, Download, Settings } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DateRangePicker } from "@/components/DateRangePicker"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

// Import our new dashboard components
import { WalletOverviewCard } from "./WalletOverviewCard"
import { SMSUsageStatistics } from "./SMSUsageStatistics"
import { DashboardMetrics } from "./DashboardMetrics"
import { DashboardCharts } from "./DashboardCharts"
import { ErrorBoundary } from "./ErrorBoundary"
import { ResponsiveContainer, MobileGrid } from "./LoadingSpinner"

interface ComprehensiveDashboardProps {
  clientId?: string
  className?: string
}

export function ComprehensiveDashboard({ 
  clientId, 
  className 
}: ComprehensiveDashboardProps) {
  const [dateRange, setDateRange] = useState<{
    start: string
    end: string
  }>({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })
  
  const [refreshing, setRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  const handleRefresh = async () => {
    setRefreshing(true)
    setLastRefresh(new Date())
    
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false)
    }, 1000)
  }

  const handleDateRangeChange = (start: string, end: string) => {
    setDateRange({ start, end })
  }

  const handleExportData = () => {
    // Implement export functionality
    console.log('Exporting dashboard data...')
  }

  return (
    <ResponsiveContainer className={cn("space-y-6", className)}>
      {/* Dashboard Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-xl md:text-2xl font-bold text-foreground">Dashboard Overview</h1>
          <p className="text-sm text-muted-foreground">
            Comprehensive SMS platform analytics and metrics
          </p>
        </div>

        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
          <DateRangePicker
            onDateRangeChange={handleDateRangeChange}
            initialStart={dateRange.start}
            initialEnd={dateRange.end}
          />
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="hidden sm:flex"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
              <span className="hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Last Updated Info */}
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>
          Data range: {new Date(dateRange.start).toLocaleDateString()} - {new Date(dateRange.end).toLocaleDateString()}
        </span>
        <span>
          Last updated: {lastRefresh.toLocaleTimeString()}
        </span>
      </div>

      {/* Main Dashboard Content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="wallet">Wallet</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6 mt-6">
          {/* Top Row - Key Metrics */}
          <MobileGrid columns={{ mobile: 1, tablet: 1, desktop: 2 }} gap={6}>
            <ErrorBoundary>
              <WalletOverviewCard
                clientId={clientId}
                onRefresh={handleRefresh}
              />
            </ErrorBoundary>
            <ErrorBoundary>
              <DashboardMetrics
                clientId={clientId}
                dateRange={dateRange}
                onRefresh={handleRefresh}
              />
            </ErrorBoundary>
          </MobileGrid>

          {/* Second Row - Usage and Charts */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            <div className="xl:col-span-2 order-2 xl:order-1">
              <ErrorBoundary>
                <DashboardCharts
                  clientId={clientId}
                  dateRange={dateRange}
                  onRefresh={handleRefresh}
                />
              </ErrorBoundary>
            </div>
            <div className="order-1 xl:order-2">
              <ErrorBoundary>
                <SMSUsageStatistics
                  clientId={clientId}
                  limit={3}
                  onRefresh={handleRefresh}
                />
              </ErrorBoundary>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 gap-6">
            <ErrorBoundary>
              <DashboardCharts
                clientId={clientId}
                dateRange={dateRange}
                onRefresh={handleRefresh}
                className="col-span-full"
              />
            </ErrorBoundary>
            <ErrorBoundary>
              <DashboardMetrics
                clientId={clientId}
                dateRange={dateRange}
                onRefresh={handleRefresh}
              />
            </ErrorBoundary>
          </div>
        </TabsContent>
        
        <TabsContent value="usage" className="space-y-6 mt-6">
          <MobileGrid columns={{ mobile: 1, tablet: 1, desktop: 2 }} gap={6}>
            <ErrorBoundary>
              <SMSUsageStatistics
                clientId={clientId}
                limit={10}
                onRefresh={handleRefresh}
              />
            </ErrorBoundary>
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Usage Insights</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <Calendar className="h-4 w-4" />
                  <AlertDescription>
                    Peak usage typically occurs between 9 AM - 5 PM on weekdays.
                    Consider scheduling campaigns during these hours for better engagement.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Quick Actions</h4>
                  <div className="grid grid-cols-1 gap-2">
                    <Button variant="outline" size="sm" className="justify-start">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure Alert Thresholds
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      <Download className="h-4 w-4 mr-2" />
                      Download Usage Report
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </MobileGrid>
        </TabsContent>
        
        <TabsContent value="wallet" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 order-2 lg:order-1">
              <ErrorBoundary>
                <WalletOverviewCard
                  clientId={clientId}
                  onRefresh={handleRefresh}
                  className="h-full"
                />
              </ErrorBoundary>
            </div>
            <Card className="bg-card border-border order-1 lg:order-2">
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Wallet Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <AlertDescription>
                    Monitor your balance regularly to ensure uninterrupted service.
                    Set up automatic top-ups to maintain optimal balance levels.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Quick Actions</h4>
                  <div className="grid grid-cols-1 gap-2">
                    <Button variant="default" size="sm" className="justify-start">
                      Top Up Balance
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      View Transaction History
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Configure Auto Top-up
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Update Alert Settings
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Footer Info */}
      <div className="text-center text-xs text-muted-foreground py-4 border-t border-border">
        <p>
          Dashboard data is updated in real-time. For technical support, contact your system administrator.
        </p>
      </div>
    </ResponsiveContainer>
  )
}
