import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User,
  AuthContextType,
  realLogin,
  mockLogin,
  mockLogout,
  mockForgotPassword,
  mockResetPassword,
  getStoredUser,
  isTokenValid
} from '@/lib/auth';
import { apiService } from '@/lib/api-index';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = getStoredUser();
        const tokenValid = isTokenValid();
        
        if (storedUser && tokenValid) {
          setUser(storedUser);
          // Set auth token in API service
          const token = localStorage.getItem('auth_token');
          if (token) {
            apiService.setAuthToken(token);
          }
        } else {
          // Clear invalid auth data
          mockLogout();
          apiService.removeAuthToken();
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        mockLogout();
        apiService.removeAuthToken();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (phoneNumber: string, password: string, countryCode: string, rememberMe?: boolean): Promise<boolean> => {
    setIsLoading(true);

    try {
      // Try real API login first, fallback to mock if needed
      let loggedInUser = await realLogin(phoneNumber, password, countryCode);

      // If real API fails, try mock login for development
      if (!loggedInUser) {
        console.log('Real API login failed, trying mock login for development...');
        loggedInUser = await mockLogin(phoneNumber, password, countryCode);
      }

      if (loggedInUser) {
        setUser(loggedInUser);

        // Set auth token in API service
        const token = localStorage.getItem('auth_token');
        if (token) {
          apiService.setAuthToken(token);
        }

        // If remember me is false, set session storage instead of local storage
        if (!rememberMe) {
          // Move to session storage
          const userData = localStorage.getItem('auth_user');
          const authToken = localStorage.getItem('auth_token');

          if (userData && authToken) {
            sessionStorage.setItem('auth_user', userData);
            sessionStorage.setItem('auth_token', authToken);
            localStorage.removeItem('auth_user');
            localStorage.removeItem('auth_token');
          }
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    mockLogout();
    apiService.removeAuthToken();
    
    // Also clear session storage
    sessionStorage.removeItem('auth_user');
    sessionStorage.removeItem('auth_token');
  };

  const forgotPassword = async (phoneNumber: string, countryCode: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      const result = await mockForgotPassword(phoneNumber, countryCode);
      return result;
    } catch (error) {
      console.error('Forgot password error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (token: string, newPassword: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      const result = await mockResetPassword(token, newPassword);
      return result;
    } catch (error) {
      console.error('Reset password error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    login,
    logout,
    forgotPassword,
    resetPassword,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
