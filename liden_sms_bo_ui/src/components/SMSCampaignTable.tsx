import { useState, useMemo } from "react";
import { Search, Eye, ChevronLeft, ChevronRight, ArrowLeft, Download, MoreVertical } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import CampaignDetailHeader from "./CampaignDetailHeader";
import DeliveryStatsDashboard from "./DeliveryStatsDashboard";
import StatusBreakdownTable from "./StatusBreakdownTable";
import MessageDetailsTable from "./MessageDetailsTable";
import CampaignSidebar from "./CampaignSidebar";

interface SMSCampaign {
  id: string;
  sender: string;
  campaignId: string;
  message: string;
  date: string;
  status: 'sent' | 'pending' | 'failed';
  recipients?: number;
  stats?: {
    totalSent: number;
    delivered: number;
    pending: number;
    failed: number;
  };
}

const sampleCampaigns: SMSCampaign[] = [
  {
    id: "1",
    sender: "23311",
    campaignId: "235259",
    message: "API MESSAGING INVOCATIONS SenderID:23311 for date:2025-09-04 API_BULK_SMS_CALL_* 23311 * 2025-09-04",
    date: "Sep 4, 2025",
    status: "sent",
    recipients: 24,
    stats: { totalSent: 24, delivered: 20, pending: 2, failed: 2 }
  },
  {
    id: "2",
    sender: "SOKA",
    campaignId: "235257",
    message: "API MESSAGING INVOCATIONS SenderID:SOKA for date:2025-09-03 API_BULK_SMS_CALL_* SOKA * 2025-09-03",
    date: "Sep 3, 2025",
    status: "sent",
    recipients: 1,
    stats: { totalSent: 1, delivered: 1, pending: 0, failed: 0 }
  },
  {
    id: "3",
    sender: "23311",
    campaignId: "235201",
    message: "API MESSAGING INVOCATIONS SenderID:23311 for date:2025-09-03 API_BULK_SMS_CALL_* 23311 * 2025-09-03",
    date: "Sep 3, 2025",
    status: "sent",
    recipients: 300,
    stats: { totalSent: 300, delivered: 280, pending: 5, failed: 15 }
  },
  {
    id: "4",
    sender: "MOSSBETS_TS",
    campaignId: "235185",
    message: "API MESSAGING INVOCATIONS SenderID:MOSSBETS_TS for date:2025-09-02 API_BULK_SMS_CALL_* MOSSBETS_TS_* 2025-09-02",
    date: "Sep 2, 2025",
    status: "sent",
    recipients: 1,
    stats: { totalSent: 1, delivered: 0, pending: 1, failed: 0 }
  },
  {
    id: "5",
    sender: "23311",
    campaignId: "235181",
    message: "API MESSAGING INVOCATIONS SenderID:23311 for date:2025-09-02 API_BULK_SMS_CALL_* 23311 * 2025-09-02",
    date: "Sep 2, 2025",
    status: "sent",
    recipients: 72,
    stats: { totalSent: 72, delivered: 65, pending: 3, failed: 4 }
  },
  {
    id: "6",
    sender: "23311",
    campaignId: "235157",
    message: "API MESSAGING INVOCATIONS SenderID:23311 for date:2025-09-01 API_BULK_SMS_CALL_* 23311 * 2025-09-01",
    date: "Sep 1, 2025",
    status: "sent",
    recipients: 80,
    stats: { totalSent: 80, delivered: 75, pending: 2, failed: 3 }
  },
  {
    id: "7",
    sender: "MOSSBETS",
    campaignId: "235132",
    message: "1st deposit ya aku aku na FREE BONUS! DEPOSIT 450ksh+ UPATE SAI! LIVERPOOL v ARSENAL BRIGHTON v MANCITY mossbets.com STOP *456*9#",
    date: "Aug 31, 2025",
    status: "sent",
    recipients: 9223,
    stats: { totalSent: 9223, delivered: 2665, pending: 6, failed: 6552 }
  },
  {
    id: "8",
    sender: "23311",
    campaignId: "235125",
    message: "API MESSAGING INVOCATIONS SenderID:23311 for date:2025-08-31 API_BULK_SMS_CALL_* 23311 * 2025-08-31",
    date: "Aug 31, 2025",
    status: "sent",
    recipients: 72,
    stats: { totalSent: 72, delivered: 68, pending: 1, failed: 3 }
  }
];

interface SMSCampaignTableProps {
  onCampaignClick?: (campaign: SMSCampaign) => void;
  onComposeMessage?: () => void;
}

export default function SMSCampaignTable({ onCampaignClick, onComposeMessage }: SMSCampaignTableProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState<'date' | 'sender' | 'campaignId'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'detail'>('list');
  const [selectedCampaign, setSelectedCampaign] = useState<SMSCampaign | null>(null);

  const filteredAndSortedCampaigns = useMemo(() => {
    let filtered = sampleCampaigns;

    // Filter by search query
    if (searchQuery.trim() !== "") {
      filtered = sampleCampaigns.filter(campaign =>
        campaign.sender.toLowerCase().includes(searchQuery.toLowerCase()) ||
        campaign.campaignId.includes(searchQuery) ||
        campaign.message.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort campaigns
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortBy) {
        case 'date':
          aValue = a.date;
          bValue = b.date;
          break;
        case 'sender':
          aValue = a.sender;
          bValue = b.sender;
          break;
        case 'campaignId':
          aValue = parseInt(a.campaignId);
          bValue = parseInt(b.campaignId);
          break;
        default:
          aValue = a.date;
          bValue = b.date;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [searchQuery, sortBy, sortOrder]);

  const paginatedCampaigns = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredAndSortedCampaigns.slice(startIndex, endIndex);
  }, [filteredAndSortedCampaigns, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredAndSortedCampaigns.length / itemsPerPage);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleCampaignClick = (campaign: SMSCampaign) => {
    setSelectedCampaign(campaign);
    setViewMode('detail');
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedCampaign(null);
  };

  const handleSelectCampaign = (campaignId: string, checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(prev => [...prev, campaignId]);
    } else {
      setSelectedCampaigns(prev => prev.filter(id => id !== campaignId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(paginatedCampaigns.map(c => c.campaignId));
    } else {
      setSelectedCampaigns([]);
    }
  };

  const isAllSelected = paginatedCampaigns.length > 0 &&
    paginatedCampaigns.every(c => selectedCampaigns.includes(c.campaignId));
  const isIndeterminate = selectedCampaigns.length > 0 && !isAllSelected;

  // Detail view
  if (viewMode === 'detail' && selectedCampaign) {
    const campaignStats = selectedCampaign.stats || {
      totalSent: 1,
      delivered: 0,
      pending: 1,
      failed: 0
    };

    const statusBreakdown = [
      { status: "DeliveredToTerminal", count: campaignStats.delivered },
      { status: "Pending", count: campaignStats.pending },
      { status: "Failed", count: campaignStats.failed }
    ].filter(item => item.count > 0);

    return (
      <div className="flex h-screen bg-slate-900">
        {/* Sidebar */}
        <CampaignSidebar onComposeMessage={onComposeMessage} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-slate-800 border-b border-slate-700">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToList}
              className="text-gray-400 hover:text-white hover:bg-slate-700"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold text-white">
                Campaign ID {selectedCampaign.campaignId}
              </h1>
              <Badge variant="outline" className="mt-1 text-xs border-slate-600 text-gray-300">
                {selectedCampaign.status.toUpperCase()}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-red-600 border-red-600 text-white hover:bg-red-700"
                >
                  Action
                  <MoreVertical className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                <DropdownMenuItem className="text-white hover:bg-slate-700">
                  Resend Campaign
                </DropdownMenuItem>
                <DropdownMenuItem className="text-white hover:bg-slate-700">
                  Duplicate Campaign
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-400 hover:bg-slate-700">
                  Delete Campaign
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

          {/* Campaign Details */}
          <div className="flex-1 p-6 space-y-6 overflow-auto">
            <DeliveryStatsDashboard
              stats={campaignStats}
              campaignMessage={selectedCampaign.message}
              senderName={selectedCampaign.sender}
              campaignId={selectedCampaign.campaignId}
            />

            {/* Status Breakdown and Message Details */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <StatusBreakdownTable
                data={statusBreakdown}
                totalCount={campaignStats.totalSent}
              />
              <MessageDetailsTable />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // List view
  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search Campaign ID and press Enter"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-700 text-white placeholder:text-gray-400"
          />
        </div>
        <Button
          variant="outline"
          className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
        >
          Select Date here
        </Button>
      </div>

      {/* Sort and Items per page controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={(value: 'date' | 'sender' | 'campaignId') => setSortBy(value)}>
            <SelectTrigger className="w-40 bg-slate-800 border-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              <SelectItem value="date" className="text-white">Sort by Date</SelectItem>
              <SelectItem value="sender" className="text-white">Sort by Sender</SelectItem>
              <SelectItem value="campaignId" className="text-white">Sort by Campaign ID</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </Button>
        </div>
        <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(parseInt(value))}>
          <SelectTrigger className="w-32 bg-slate-800 border-slate-700 text-white">
            <SelectValue />
          </SelectTrigger>
          <SelectContent className="bg-slate-800 border-slate-700">
            <SelectItem value="5" className="text-white">5 per page</SelectItem>
            <SelectItem value="10" className="text-white">10 per page</SelectItem>
            <SelectItem value="20" className="text-white">20 per page</SelectItem>
            <SelectItem value="50" className="text-white">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Select All Checkbox */}
      {paginatedCampaigns.length > 0 && (
        <div className="flex items-center gap-2 px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg">
          <Checkbox
            checked={isAllSelected}
            onCheckedChange={handleSelectAll}
            className="border-slate-600 data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600"
          />
          <span className="text-sm text-gray-300">
            Select All ({selectedCampaigns.length} selected)
          </span>
        </div>
      )}

      {/* Campaign List */}
      <div className="space-y-3">
        {paginatedCampaigns.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            No Schedule Message Found
          </div>
        ) : (
          paginatedCampaigns.map((campaign) => (
            <div
              key={campaign.id}
              className="bg-slate-800 border border-slate-700 rounded-lg p-4 hover:bg-slate-700 transition-colors"
            >
              <div className="flex items-start gap-3">
                {/* Checkbox */}
                <Checkbox
                  checked={selectedCampaigns.includes(campaign.campaignId)}
                  onCheckedChange={(checked) => handleSelectCampaign(campaign.campaignId, checked as boolean)}
                  className="mt-1 border-slate-600 data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600"
                />

                {/* Campaign Content */}
                <div
                  className="flex-1 min-w-0 cursor-pointer"
                  onClick={() => handleCampaignClick(campaign)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-red-400 font-medium text-sm">
                          Campaign ID# {campaign.campaignId} via {campaign.sender}
                        </span>
                      </div>
                      <div className="text-xs text-gray-400 mb-2">
                        {campaign.message.length > 100
                          ? `${campaign.message.substring(0, 100)}...`
                          : campaign.message}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-400">
                        <span>{campaign.date}</span>
                        <span>Recipients: {campaign.recipients || 0}</span>
                        <span>Sent by: API_Liden</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between pt-4">
          <div className="text-sm text-gray-400">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredAndSortedCampaigns.length)} of {filteredAndSortedCampaigns.length} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700 disabled:opacity-50"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-8 h-8 p-0 ${
                      currentPage === pageNum
                        ? "bg-red-600 text-white"
                        : "bg-slate-800 border-slate-700 text-white hover:bg-slate-700"
                    }`}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="bg-slate-800 border-slate-700 text-white hover:bg-slate-700 disabled:opacity-50"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
