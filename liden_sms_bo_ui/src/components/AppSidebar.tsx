import { useState } from "react"
import { 
  BarChart3, 
  MessageSquare, 
  Users, 
  FileText, 
  DollarSign, 
  Clock, 
  CreditCard, 
  Receipt, 
  User,
  Settings,
  ChevronRight,
  Home,
  Send,
  BarChart,
  Inbox,
  UserPlus,
  Shield
} from "lucide-react"
import { NavLink, useLocation } from "react-router-dom"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { TooltipSubmenu } from "@/components/TooltipSubmenu"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

const statsItems = [
  { title: "Dashboards", url: "/", icon: Home },
  { title: "Analytics", url: "/analytics", icon: BarChart3 },
]

const appsItems = [
  { title: "SMS", url: "/sms", icon: MessageSquare, 
    subItems: [
      { title: "Bulk SMS", url: "/sms/bulk", icon: Send,
        subItems: [
          { title: "Outbox", url: "/sms/bulk/outbox", icon: MessageSquare },
          { title: "Analytic", url: "/sms/bulk/analytic", icon: BarChart },
          { title: "Message", url: "/sms/bulk/message", icon: MessageSquare },
          { title: "Blacklist", url: "/sms/bulk/blacklist", icon: Shield },
        ]
      },
      { title: "Premium", url: "/sms/premium", icon: CreditCard },
      { title: "Shortcode", url: "/sms/shortcode", icon: MessageSquare },
      { title: "Alphanumeric", url: "/sms/alphanumeric", icon: MessageSquare },
    ]
  },
  { title: "Inbox", url: "/inbox", icon: Inbox },
  { title: "Contact", url: "/contact", icon: Users,
    subItems: [
      { title: "View Contacts", url: "/contact/view", icon: Users },
      { title: "Groups", url: "/contact/groups", icon: Users },
    ]
  },
  { title: "Survey", url: "/survey", icon: FileText,
    subItems: [
      { title: "View Stats", url: "/survey/stats", icon: BarChart },
      { title: "Create Survey", url: "/survey/create", icon: UserPlus },
    ]
  },
  { title: "USSD", url: "/ussd", icon: MessageSquare },
  { title: "Airtime", url: "/airtime", icon: DollarSign },
  { title: "Billing", url: "/billing", icon: CreditCard },
]

const settingsItems = [
  { title: "Services Request", url: "/settings/services-request", icon: Settings },
  { title: "BulkRate Card", url: "/settings/bulk-rate-card", icon: CreditCard },
  { title: "Invoice", url: "/settings/invoice", icon: Receipt },
  { title: "User", url: "/settings/user-management", icon: User },
  { title: "Sender IDs", url: "/settings/sender-ids", icon: Shield },
]

export function AppSidebar() {
  const { open, setOpen, openMobile, setOpenMobile } = useSidebar()
  const isMobile = useIsMobile()
  const location = useLocation()
  const currentPath = location.pathname
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const isActive = (path: string) => currentPath === path
  
  const isParentActive = (item: any): boolean => {
    if (isActive(item.url)) return true
    if (item.subItems) {
      return item.subItems.some((subItem: any) => isParentActive(subItem))
    }
    return false
  }

  const toggleExpanded = (title: string) => {
    const isOpen = isMobile ? openMobile : open
    if (!isOpen) {
      // If sidebar is collapsed, don't toggle, let tooltip handle it
      return
    }
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const renderMenuItem = (item: any, depth = 0) => {
    const hasSubItems = item.subItems && item.subItems.length > 0
    const isExpanded = expandedItems.includes(item.title)
    const itemIsActive = isActive(item.url)
    const parentIsActive = isParentActive(item)
    const isOpen = isMobile ? openMobile : open
    const isSubItem = depth > 0

    const menuContent = (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton 
          asChild={!hasSubItems}
          className={cn(
            "group relative transition-all duration-200 ease-in-out",
            isSubItem && "text-sm",
            (itemIsActive || (hasSubItems && parentIsActive)) && "bg-primary text-primary-foreground font-medium",
            !itemIsActive && !parentIsActive && "hover:bg-sidebar-accent text-sidebar-foreground"
          )}
          style={{ marginLeft: `${depth * 1}rem` }}
        >
          {hasSubItems ? (
            <div
              onClick={() => toggleExpanded(item.title)}
              className="w-full flex items-center justify-between cursor-pointer"
            >
              <div className="flex items-center">
                <item.icon className="mr-3 h-4 w-4" />
                {isOpen && <span>{item.title}</span>}
              </div>
              {isOpen && (
                <ChevronRight
                  className={cn(
                    "h-4 w-4 transition-transform duration-200",
                    isExpanded && "rotate-90"
                  )}
                />
              )}
            </div>
          ) : (
            <NavLink 
              to={item.url} 
              className="flex items-center w-full"
              onClick={() => {
                if (isMobile) {
                  setOpenMobile(false)
                }
              }}
            >
              <item.icon className="mr-3 h-4 w-4" />
              {isOpen && <span>{item.title}</span>}
            </NavLink>
          )}
        </SidebarMenuButton>
        {hasSubItems && isExpanded && isOpen && (
          <div className="mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out">
            {item.subItems.map((subItem: any) => renderMenuItem(subItem, depth + 1))}
          </div>
        )}
      </SidebarMenuItem>
    )

    // Wrap with tooltip if has subitems and sidebar is collapsed
    if (hasSubItems && !isOpen && depth === 0) {
      const renderNestedSubmenu = (items: any[], level = 0) => (
        <div className={cn("space-y-1", level > 0 && "ml-4")}>
          {items.map((subItem: any) => (
            <div key={subItem.title}>
              {subItem.subItems ? (
                <div>
                  <div className="font-medium text-xs text-muted-foreground mb-1 px-2">
                    {subItem.title}
                  </div>
                  {renderNestedSubmenu(subItem.subItems, level + 1)}
                </div>
              ) : (
                <NavLink
                  to={subItem.url}
                  className="flex items-center p-2 text-sm rounded hover:bg-accent transition-colors"
                  onClick={() => {
                    if (isMobile) {
                      setOpenMobile(false)
                    }
                  }}
                >
                  <subItem.icon className="mr-2 h-4 w-4" />
                  {subItem.title}
                </NavLink>
              )}
            </div>
          ))}
        </div>
      )

      const submenuContent = (
        <div className="p-2 min-w-[200px] max-w-[300px]">
          <div className="font-medium text-sm mb-2">{item.title}</div>
          {renderNestedSubmenu(item.subItems)}
        </div>
      )

      return (
        <TooltipSubmenu content={submenuContent}>
          {menuContent}
        </TooltipSubmenu>
      )
    }

    return menuContent
  }

  const isOpen = isMobile ? openMobile : open

  return (
    <>
      {/* Mobile overlay background */}
      {isMobile && openMobile && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setOpenMobile(false)}
        />
      )}
      
      <Sidebar 
        className={cn(
          "transition-all duration-300 ease-in-out border-r-0",
          isMobile && "fixed left-0 top-0 z-50 h-full",
          isMobile && !openMobile && "-translate-x-full",
          !isMobile && (open ? "w-64" : "w-16")
        )}
        collapsible="icon"
      >
        <SidebarContent className="bg-sidebar border-r border-sidebar-border h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
            {isOpen ? (
              <div className="flex items-center space-x-2">
                <img
                  src="https://app.liden.co.ke/img/logo2.760c0f46.jpeg"
                  alt="Liden Logo"
                  className="w-8 h-8 rounded object-contain"
                />
                <span className="font-semibold text-sidebar-foreground">Liden SMS</span>
              </div>
            ) : (
              <div className="flex items-center justify-center w-full">
                <img
                  src="https://app.liden.co.ke/img/logo2.760c0f46.jpeg"
                  alt="Liden Logo"
                  className="w-8 h-8 rounded object-contain"
                />
              </div>
            )}
            {!isMobile && <SidebarTrigger className="ml-auto" />}
          </div>

          {/* Navigation Groups */}
          <SidebarGroup>
            <SidebarGroupLabel className="text-xs uppercase tracking-wider text-sidebar-foreground/60">
              {isOpen ? "STATS" : ""}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {statsItems.map((item) => renderMenuItem(item))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel className="text-xs uppercase tracking-wider text-sidebar-foreground/60">
              {isOpen ? "APPS" : ""}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {appsItems.map((item) => renderMenuItem(item))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel className="text-xs uppercase tracking-wider text-sidebar-foreground/60">
              {isOpen ? "SETTINGS" : ""}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {settingsItems.map((item) => renderMenuItem(item))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </>
  )
}