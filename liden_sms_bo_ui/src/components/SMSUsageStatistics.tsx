import { useState, useEffect } from "react"
import { MessageSquare, TrendingUp, DollarSign, Clock, RefreshCw, AlertCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { ClientAPI } from "@/lib/api-extended"
import { BulkUsageResponse, BulkUsageParams } from "@/lib/api-types"

interface SMSUsageStatisticsProps {
  clientId?: string
  className?: string
  limit?: number
  onRefresh?: () => void
}

export function SMSUsageStatistics({ 
  clientId, 
  className,
  limit = 5,
  onRefresh 
}: SMSUsageStatisticsProps) {
  const [usageData, setUsageData] = useState<BulkUsageResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  const fetchUsageData = async () => {
    try {
      setError(null)
      const params: BulkUsageParams = {
        limit,
        clientId,
        status: 400 // As specified in requirements
      }
      
      const response = await ClientAPI.getBulkUsage(params)
      
      if (response.success && response.data) {
        setUsageData(response.data)
      } else {
        setError(response.message || 'Failed to fetch SMS usage data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchUsageData()
    onRefresh?.()
  }

  useEffect(() => {
    fetchUsageData()
  }, [clientId, limit])

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'KES',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 200: return 'bg-green-100 text-green-800'
      case 400: return 'bg-blue-100 text-blue-800'
      case 500: return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: number) => {
    switch (status) {
      case 200: return 'Completed'
      case 400: return 'In Progress'
      case 500: return 'Failed'
      default: return 'Unknown'
    }
  }

  const calculateTotals = () => {
    if (!usageData.length) return null
    
    return usageData.reduce((acc, campaign) => ({
      totalSent: acc.totalSent + campaign.totalSent,
      totalDelivered: acc.totalDelivered + campaign.totalDelivered,
      totalFailed: acc.totalFailed + campaign.totalFailed,
      totalPending: acc.totalPending + campaign.totalPending,
      totalCost: acc.totalCost + campaign.totalCost,
    }), {
      totalSent: 0,
      totalDelivered: 0,
      totalFailed: 0,
      totalPending: 0,
      totalCost: 0,
    })
  }

  const totals = calculateTotals()

  if (loading) {
    return (
      <Card className={cn("bg-card border-border", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-full" />
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn("bg-card border-border border-red-200", className)}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">Error loading SMS usage data</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="mt-3"
            disabled={refreshing}
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("bg-card border-border transition-all duration-200 hover:shadow-lg", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            <span>SMS Usage Statistics</span>
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4 mt-4">
            {totals && (
              <>
                {/* Summary Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{totals.totalSent.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Total Sent</div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{totals.totalDelivered.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Delivered</div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{totals.totalFailed.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Failed</div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">{totals.totalPending.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Pending</div>
                  </div>
                </div>

                {/* Delivery Rate Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Overall Delivery Rate</span>
                    <span className="font-medium">
                      {formatPercentage((totals.totalDelivered / totals.totalSent) * 100)}
                    </span>
                  </div>
                  <Progress 
                    value={(totals.totalDelivered / totals.totalSent) * 100} 
                    className="h-2"
                  />
                </div>

                {/* Cost Summary */}
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-muted-foreground">Total Cost</span>
                  </div>
                  <span className="text-lg font-bold">
                    {formatCurrency(totals.totalCost, usageData[0]?.currency || 'KES')}
                  </span>
                </div>
              </>
            )}
          </TabsContent>
          
          <TabsContent value="campaigns" className="space-y-4 mt-4">
            {usageData.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No SMS campaigns found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {usageData.map((campaign) => (
                  <div key={campaign.campaignId} className="border border-border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{campaign.campaignName}</h4>
                        <p className="text-xs text-muted-foreground">ID: {campaign.campaignId}</p>
                      </div>
                      <Badge className={getStatusColor(campaign.status)}>
                        {getStatusLabel(campaign.status)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">Sent</div>
                        <div className="font-medium">{campaign.totalSent.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Delivery Rate</div>
                        <div className="font-medium text-green-600">
                          {formatPercentage(campaign.deliveryRate)}
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Cost</div>
                        <div className="font-medium">
                          {formatCurrency(campaign.totalCost, campaign.currency)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Delivered: {campaign.totalDelivered}</span>
                        <span>Failed: {campaign.totalFailed}</span>
                        <span>Pending: {campaign.totalPending}</span>
                      </div>
                      <Progress value={campaign.deliveryRate} className="h-1" />
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      Created: {new Date(campaign.createdDate).toLocaleDateString()}
                      {campaign.completedDate && (
                        <span> • Completed: {new Date(campaign.completedDate).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
